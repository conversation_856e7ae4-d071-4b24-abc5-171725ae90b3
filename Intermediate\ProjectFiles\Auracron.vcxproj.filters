<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <None Include="..\..\Auracron.uproject" />
    <Filter Include="Source">
      <UniqueIdentifier>{F31BBDD1-B3E8-3BCC-9652-680E16935819}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\Auracron.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\Source\AuracronEditor.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\.vsconfig" />
    <None Include="..\..\unreal_mcp.log" />
    <Filter Include="Config">
      <UniqueIdentifier>{FA535FFB-25E1-3D20-B416-52F9BE21E06E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Config\DefaultEditor.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultEngine.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGame.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultInput.ini">
      <Filter>Config</Filter>
    </None>
    <Filter Include="Plugins">
      <UniqueIdentifier>{BB38096A-B391-30DC-A0D4-4F3EA6B44507}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\UnrealMCP">
      <UniqueIdentifier>{D741E9D7-2443-3229-946E-239D2700AD33}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\UnrealMCP\Source">
      <UniqueIdentifier>{AA5862E2-2B5E-3C54-B14F-C9ADE0CB000C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP">
      <UniqueIdentifier>{A8C084DA-1928-35F8-8A07-EB43CE50FCDC}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\UnrealMCP.Build.cs">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP</Filter>
    </None>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP\Private">
      <UniqueIdentifier>{D919B08C-445D-3FCD-9DFE-48A112DE3341}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\MCPServerRunnable.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPBridge.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPModule.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands">
      <UniqueIdentifier>{511278AC-FC06-333C-84C2-B5EDF8469817}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPAICommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPAnalyticsTelemetryCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPAudioSystemCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBackendServicesCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBlueprintCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBlueprintNodeCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPChaosPhysicsCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCloudServicesCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCollisionCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCombatMechanicsCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCommonUtils.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPEditorCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPGamePhasesCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPHardwareDetectionCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPJungleSystemCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPLaneMechanicsCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMaterialCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMultilayerMapCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPNetworkCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPNetworkingCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPNiagaraCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPObjectivesStructuresCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPPathfindingCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPPerformanceCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPPlatformCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPProceduralCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPProjectCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPRealmCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPRenderingPipelineCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPSecurityAntiCheatCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPUMGCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPVerticalNavigationCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPVisionCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPWorldPartitionCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP\Public">
      <UniqueIdentifier>{AFF61063-E844-32A0-A18E-7FE47B9AEB77}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\MCPServerRunnable.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\UnrealMCPBridge.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\UnrealMCPModule.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public</Filter>
    </ClInclude>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands">
      <UniqueIdentifier>{7460A6C2-A1BB-3667-ACB1-35E16D0E8F89}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPAICommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPAnalyticsTelemetryCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPAudioSystemCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPBackendServicesCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPBlueprintCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPBlueprintNodeCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPChaosPhysicsCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCloudServicesCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCollisionCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCombatMechanicsCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCommonUtils.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPEditorCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPGamePhasesCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPHardwareDetectionCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPJungleSystemCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPLaneMechanicsCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPMaterialCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPMultilayerMapCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPNetworkCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPNetworkingCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPNiagaraCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPObjectivesStructuresCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPPathfindingCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPPerformanceCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPPlatformCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPProceduralCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPProjectCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPRealmCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPRenderingPipelineCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPSecurityAntiCheatCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPUMGCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPVerticalNavigationCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPVisionCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPWorldPartitionCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <None Include="..\..\Plugins\UnrealMCP\UnrealMCP.uplugin">
      <Filter>Plugins\UnrealMCP</Filter>
    </None>
    <Filter Include="Source\Auracron">
      <UniqueIdentifier>{2621590F-30BC-3225-8725-B519E32DD9FD}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\Auracron\Auracron.Build.cs">
      <Filter>Source\Auracron</Filter>
    </None>
    <ClCompile Include="..\..\Source\Auracron\Auracron.cpp">
      <Filter>Source\Auracron</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\Auracron\Auracron.h">
      <Filter>Source\Auracron</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
