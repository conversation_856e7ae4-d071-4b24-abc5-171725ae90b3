<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Invalid|x64">
      <Configuration>Invalid</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Invalid|arm64">
      <Configuration>Invalid</Configuration>
      <Platform>arm64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Invalid|arm64ec">
      <Configuration>Invalid</Configuration>
      <Platform>arm64ec</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame|x64">
      <Configuration>DebugGame</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame|arm64">
      <Configuration>DebugGame</Configuration>
      <Platform>arm64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame|arm64ec">
      <Configuration>DebugGame</Configuration>
      <Platform>arm64ec</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame_Editor|x64">
      <Configuration>DebugGame_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame_Editor|arm64">
      <Configuration>DebugGame_Editor</Configuration>
      <Platform>arm64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DebugGame_Editor|arm64ec">
      <Configuration>DebugGame_Editor</Configuration>
      <Platform>arm64ec</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development|x64">
      <Configuration>Development</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development|arm64">
      <Configuration>Development</Configuration>
      <Platform>arm64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development|arm64ec">
      <Configuration>Development</Configuration>
      <Platform>arm64ec</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development_Editor|x64">
      <Configuration>Development_Editor</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development_Editor|arm64">
      <Configuration>Development_Editor</Configuration>
      <Platform>arm64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development_Editor|arm64ec">
      <Configuration>Development_Editor</Configuration>
      <Platform>arm64ec</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|x64">
      <Configuration>Shipping</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|arm64">
      <Configuration>Shipping</Configuration>
      <Platform>arm64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|arm64ec">
      <Configuration>Shipping</Configuration>
      <Platform>arm64ec</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5FE55E88-AE8E-334C-99D2-EB9D55BD64EA}</ProjectGuid>
    <RootNamespace>Auracron</RootNamespace>
  </PropertyGroup>
  <Import Project="UECommon.props" />
  <ImportGroup Label="ExtensionSettings" />
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <NMakePreprocessorDefinitions>$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <IncludePath>$(IncludePath);..\Build\Win64\x64\AuracronEditor\Development\UnrealEd;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ActorPickerMode\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ActorPickerMode\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AdvancedPreviewScene\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AdvancedPreviewScene\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnalyticsET\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnalyticsET\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Analytics\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Analytics\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationBlueprintEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationBlueprintEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationDataController\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationDataController\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditMode\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditMode\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationWidgets\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimationWidgets\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ApplicationCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ApplicationCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetDefinition\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetDefinition\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetRegistry\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetRegistry\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTagsEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTagsEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTools\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AssetTools\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioExtensions\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioExtensions\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkEngine\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioLinkEngine\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerXAudio2\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixerXAudio2\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixer\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioMixer\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioPlatformConfiguration\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AudioPlatformConfiguration\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationController\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationController\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationTest\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutomationTest\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutoRTFM\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AutoRTFM\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BlueprintGraph\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BlueprintGraph\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosSolverEngine\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosSolverEngine\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosVDRuntime\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosVDRuntime\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Chaos\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Chaos\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClassViewer\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClassViewer\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothSysRuntimeIntrfc\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothSysRuntimeIntrfc\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CollectionManager\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CollectionManager\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CommonMenuExtensions\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CommonMenuExtensions\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Constraints\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Constraints\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowserData\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowserData\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowser\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ContentBrowser\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookOnTheFly\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CookOnTheFly\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreOnline\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreOnline\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CorePreciseFP\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CorePreciseFP\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\VerseVMBytecode;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CoreUObject\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Core\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEngine\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEngine\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowSimulation\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowSimulation\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DesktopPlatform\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DesktopPlatform\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DetailCustomizations\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DetailCustomizations\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperSettings\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperSettings\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperToolSettings\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DeveloperToolSettings\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DirectoryWatcher\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DirectoryWatcher\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Documentation\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Documentation\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorConfig\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorConfig\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorFramework\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorFramework\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorSubsystem\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorSubsystem\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineMessages\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineMessages\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineSettings\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EngineSettings\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Engine\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Engine\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldNotification\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldNotification\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldSystemEngine\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FieldSystemEngine\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FunctionalTesting\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\FunctionalTesting\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTags\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTags\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTasks\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayTasks\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCollectionEngine\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCollectionEngine\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GraphEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GraphEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HeadMountedDisplay\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HeadMountedDisplay\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Horde\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Horde\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HTTP\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\HTTP\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageWrapper\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ImageWrapper\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InputCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InputCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InteractiveToolsFramework\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InteractiveToolsFramework\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeEngine\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\InterchangeEngine\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreOnDemandCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreOnDemandCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreOnDemand\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IoStoreOnDemand\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IrisCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\IrisCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ISMPool\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ISMPool\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\JsonUtilities\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\JsonUtilities\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Json\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Json\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\KismetCompiler\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\KismetCompiler\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Kismet\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Kismet\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Landscape\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Landscape\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Localization\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Localization\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MainFrame\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MainFrame\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialUtilities\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MaterialUtilities\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Merge\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Merge\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshBuilder\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshBuilder\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshDescription\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshDescription\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshMergeUtilities\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshMergeUtilities\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshReductionInterface\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshReductionInterface\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilitiesCommon\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilitiesCommon\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilities\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshUtilities\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessagingCommon\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessagingCommon\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Messaging\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Messaging\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneCapture\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneCapture\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneTracks\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieSceneTracks\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieScene\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MovieScene\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MSQS\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MSQS\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NavigationSystem\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NavigationSystem\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCommon\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCommon\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkFileSystem\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkFileSystem\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Networking\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Networking\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkReplayStreaming\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NetworkReplayStreaming\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NNE\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\NNE\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OpenGLDrv\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\OpenGLDrv\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PacketHandler\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PacketHandler\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PakFile\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PakFile\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Persona\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Persona\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsUtilities\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PhysicsUtilities\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Projects\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Projects\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyPath\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyPath\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RawMesh\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RawMesh\VNI;$(DefaultSystemIncludePaths);</IncludePath>
    <NMakeForcedIncludes>$(NMakeForcedIncludes)</NMakeForcedIncludes>
    <NMakeAssemblySearchPath>$(NMakeAssemblySearchPath)</NMakeAssemblySearchPath>
    <AdditionalOptions>/std:c++20  /DSAL_NO_ATTRIBUTE_DECLARATIONS=1 /permissive- /Zc:strictStrings- /Zc:__cplusplus /Yu"$(SolutionDir)Intermediate\Build\Win64\x64\AuracronEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h"</AdditionalOptions>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Auracron-Win64-DebugGame.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|arm64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Auracron-Win64-DebugGamearm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|arm64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|arm64ec'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Auracron-Win64-DebugGamearm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame|arm64ec'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Auracron Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Win64-DebugGame.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|arm64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\AuracronEditor-Win64-DebugGamearm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|arm64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|arm64ec'">
    <NMakeBuildCommandLine>$(BuildBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\AuracronEditor-Win64-DebugGamearm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='DebugGame_Editor|arm64ec'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) AuracronEditor Win64 DebugGame -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Auracron.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Development|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|arm64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Auracronarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Development|arm64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|arm64ec'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Auracronarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Development|arm64ec'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Auracron Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|arm64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\AuracronEditorarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|arm64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|arm64ec'">
    <NMakeBuildCommandLine>$(BuildBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\AuracronEditorarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Development_Editor|arm64ec'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) AuracronEditor Win64 Development -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Auracron-Win64-Shipping.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=x64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|arm64'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Auracron-Win64-Shippingarm64.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|arm64'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64 -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|arm64ec'">
    <NMakeBuildCommandLine>$(BuildBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>$(RebuildBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>$(CleanBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec</NMakeCleanCommandLine>
    <NMakeOutput>..\..\Binaries\Win64\Auracron-Win64-Shippingarm64ec.exe</NMakeOutput>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|arm64ec'">
    <NMakeCompile>
      <NMakeCompileFileCommandLine>$(BuildBatchScript) Auracron Win64 Shipping -Project="$(SolutionDir)Auracron.uproject" -WaitMutex -FromMsBuild -architecture=arm64ec -WorkingDir=$(MSBuildProjectDirectory) -Files=$(SelectedFiles)</NMakeCompileFileCommandLine>
    </NMakeCompile>
  </ItemDefinitionGroup>
  <PropertyGroup>
    <ProjectAdditionalIncludeDirectories>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHI\UHT</ProjectAdditionalIncludeDirectories>
    <ProjectAdditionalIncludeDirectories_1>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHI\VNI</ProjectAdditionalIncludeDirectories_1>
    <ProjectAdditionalIncludeDirectories_2>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RSA\UHT</ProjectAdditionalIncludeDirectories_2>
    <ProjectAdditionalIncludeDirectories_3>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RSA\VNI</ProjectAdditionalIncludeDirectories_3>
    <ProjectAdditionalIncludeDirectories_4>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ReliableHComp\UHT</ProjectAdditionalIncludeDirectories_4>
    <ProjectAdditionalIncludeDirectories_5>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ReliableHComp\VNI</ProjectAdditionalIncludeDirectories_5>
    <ProjectAdditionalIncludeDirectories_6>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RenderCore\UHT</ProjectAdditionalIncludeDirectories_6>
    <ProjectAdditionalIncludeDirectories_7>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RenderCore\VNI</ProjectAdditionalIncludeDirectories_7>
    <ProjectAdditionalIncludeDirectories_8>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Renderer\UHT</ProjectAdditionalIncludeDirectories_8>
    <ProjectAdditionalIncludeDirectories_9>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Renderer\VNI</ProjectAdditionalIncludeDirectories_9>
    <ProjectAdditionalIncludeDirectories_10>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SandboxFile\UHT</ProjectAdditionalIncludeDirectories_10>
    <ProjectAdditionalIncludeDirectories_11>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SandboxFile\VNI</ProjectAdditionalIncludeDirectories_11>
    <ProjectAdditionalIncludeDirectories_12>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneDepthPickerMode\UHT</ProjectAdditionalIncludeDirectories_12>
    <ProjectAdditionalIncludeDirectories_13>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneDepthPickerMode\VNI</ProjectAdditionalIncludeDirectories_13>
    <ProjectAdditionalIncludeDirectories_14>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Settings\UHT</ProjectAdditionalIncludeDirectories_14>
    <ProjectAdditionalIncludeDirectories_15>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Settings\VNI</ProjectAdditionalIncludeDirectories_15>
    <ProjectAdditionalIncludeDirectories_16>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SignalProcessing\UHT</ProjectAdditionalIncludeDirectories_16>
    <ProjectAdditionalIncludeDirectories_17>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SignalProcessing\VNI</ProjectAdditionalIncludeDirectories_17>
    <ProjectAdditionalIncludeDirectories_18>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshDescription\UHT</ProjectAdditionalIncludeDirectories_18>
    <ProjectAdditionalIncludeDirectories_19>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshDescription\VNI</ProjectAdditionalIncludeDirectories_19>
    <ProjectAdditionalIncludeDirectories_20>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletonEditor\UHT</ProjectAdditionalIncludeDirectories_20>
    <ProjectAdditionalIncludeDirectories_21>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletonEditor\VNI</ProjectAdditionalIncludeDirectories_21>
    <ProjectAdditionalIncludeDirectories_22>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SlateCore\UHT</ProjectAdditionalIncludeDirectories_22>
    <ProjectAdditionalIncludeDirectories_23>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SlateCore\VNI</ProjectAdditionalIncludeDirectories_23>
    <ProjectAdditionalIncludeDirectories_24>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Slate\UHT</ProjectAdditionalIncludeDirectories_24>
    <ProjectAdditionalIncludeDirectories_25>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Slate\VNI</ProjectAdditionalIncludeDirectories_25>
    <ProjectAdditionalIncludeDirectories_26>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Sockets\UHT</ProjectAdditionalIncludeDirectories_26>
    <ProjectAdditionalIncludeDirectories_27>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Sockets\VNI</ProjectAdditionalIncludeDirectories_27>
    <ProjectAdditionalIncludeDirectories_28>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceControl\UHT</ProjectAdditionalIncludeDirectories_28>
    <ProjectAdditionalIncludeDirectories_29>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SourceControl\VNI</ProjectAdditionalIncludeDirectories_29>
    <ProjectAdditionalIncludeDirectories_30>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StateStream\UHT</ProjectAdditionalIncludeDirectories_30>
    <ProjectAdditionalIncludeDirectories_31>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StateStream\VNI</ProjectAdditionalIncludeDirectories_31>
    <ProjectAdditionalIncludeDirectories_32>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshDescription\UHT</ProjectAdditionalIncludeDirectories_32>
    <ProjectAdditionalIncludeDirectories_33>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshDescription\VNI</ProjectAdditionalIncludeDirectories_33>
    <ProjectAdditionalIncludeDirectories_34>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StatusBar\UHT</ProjectAdditionalIncludeDirectories_34>
    <ProjectAdditionalIncludeDirectories_35>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StatusBar\VNI</ProjectAdditionalIncludeDirectories_35>
    <ProjectAdditionalIncludeDirectories_36>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StructViewer\UHT</ProjectAdditionalIncludeDirectories_36>
    <ProjectAdditionalIncludeDirectories_37>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StructViewer\VNI</ProjectAdditionalIncludeDirectories_37>
    <ProjectAdditionalIncludeDirectories_38>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectDataInterface\UHT</ProjectAdditionalIncludeDirectories_38>
    <ProjectAdditionalIncludeDirectories_39>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectDataInterface\VNI</ProjectAdditionalIncludeDirectories_39>
    <ProjectAdditionalIncludeDirectories_40>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectEditor\UHT</ProjectAdditionalIncludeDirectories_40>
    <ProjectAdditionalIncludeDirectories_41>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SubobjectEditor\VNI</ProjectAdditionalIncludeDirectories_41>
    <ProjectAdditionalIncludeDirectories_42>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SynthBenchmark\UHT</ProjectAdditionalIncludeDirectories_42>
    <ProjectAdditionalIncludeDirectories_43>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SynthBenchmark\VNI</ProjectAdditionalIncludeDirectories_43>
    <ProjectAdditionalIncludeDirectories_44>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetPlatform\UHT</ProjectAdditionalIncludeDirectories_44>
    <ProjectAdditionalIncludeDirectories_45>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TargetPlatform\VNI</ProjectAdditionalIncludeDirectories_45>
    <ProjectAdditionalIncludeDirectories_46>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureBuildUtilities\UHT</ProjectAdditionalIncludeDirectories_46>
    <ProjectAdditionalIncludeDirectories_47>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureBuildUtilities\VNI</ProjectAdditionalIncludeDirectories_47>
    <ProjectAdditionalIncludeDirectories_48>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\UHT</ProjectAdditionalIncludeDirectories_48>
    <ProjectAdditionalIncludeDirectories_49>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureFormat\VNI</ProjectAdditionalIncludeDirectories_49>
    <ProjectAdditionalIncludeDirectories_50>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TimeManagement\UHT</ProjectAdditionalIncludeDirectories_50>
    <ProjectAdditionalIncludeDirectories_51>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TimeManagement\VNI</ProjectAdditionalIncludeDirectories_51>
    <ProjectAdditionalIncludeDirectories_52>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenusEditor\UHT</ProjectAdditionalIncludeDirectories_52>
    <ProjectAdditionalIncludeDirectories_53>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenusEditor\VNI</ProjectAdditionalIncludeDirectories_53>
    <ProjectAdditionalIncludeDirectories_54>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenus\UHT</ProjectAdditionalIncludeDirectories_54>
    <ProjectAdditionalIncludeDirectories_55>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolMenus\VNI</ProjectAdditionalIncludeDirectories_55>
    <ProjectAdditionalIncludeDirectories_56>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolWidgets\UHT</ProjectAdditionalIncludeDirectories_56>
    <ProjectAdditionalIncludeDirectories_57>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ToolWidgets\VNI</ProjectAdditionalIncludeDirectories_57>
    <ProjectAdditionalIncludeDirectories_58>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\UHT</ProjectAdditionalIncludeDirectories_58>
    <ProjectAdditionalIncludeDirectories_59>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceLog\VNI</ProjectAdditionalIncludeDirectories_59>
    <ProjectAdditionalIncludeDirectories_60>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementFramework\UHT</ProjectAdditionalIncludeDirectories_60>
    <ProjectAdditionalIncludeDirectories_61>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementFramework\VNI</ProjectAdditionalIncludeDirectories_61>
    <ProjectAdditionalIncludeDirectories_62>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementRuntime\UHT</ProjectAdditionalIncludeDirectories_62>
    <ProjectAdditionalIncludeDirectories_63>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TypedElementRuntime\VNI</ProjectAdditionalIncludeDirectories_63>
    <ProjectAdditionalIncludeDirectories_64>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UELibSampleRate\UHT</ProjectAdditionalIncludeDirectories_64>
    <ProjectAdditionalIncludeDirectories_65>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UELibSampleRate\VNI</ProjectAdditionalIncludeDirectories_65>
    <ProjectAdditionalIncludeDirectories_66>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UMG\UHT</ProjectAdditionalIncludeDirectories_66>
    <ProjectAdditionalIncludeDirectories_67>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UMG\VNI</ProjectAdditionalIncludeDirectories_67>
    <ProjectAdditionalIncludeDirectories_68>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UncontrolledChangelists\UHT</ProjectAdditionalIncludeDirectories_68>
    <ProjectAdditionalIncludeDirectories_69>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UncontrolledChangelists\VNI</ProjectAdditionalIncludeDirectories_69>
    <ProjectAdditionalIncludeDirectories_70>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UniversalObjectLocator\UHT</ProjectAdditionalIncludeDirectories_70>
    <ProjectAdditionalIncludeDirectories_71>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UniversalObjectLocator\VNI</ProjectAdditionalIncludeDirectories_71>
    <ProjectAdditionalIncludeDirectories_72>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEdMessages\UHT</ProjectAdditionalIncludeDirectories_72>
    <ProjectAdditionalIncludeDirectories_73>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEdMessages\VNI</ProjectAdditionalIncludeDirectories_73>
    <ProjectAdditionalIncludeDirectories_74>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEd\UHT</ProjectAdditionalIncludeDirectories_74>
    <ProjectAdditionalIncludeDirectories_75>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealEd\VNI</ProjectAdditionalIncludeDirectories_75>
    <ProjectAdditionalIncludeDirectories_76>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VREditor\UHT</ProjectAdditionalIncludeDirectories_76>
    <ProjectAdditionalIncludeDirectories_77>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VREditor\VNI</ProjectAdditionalIncludeDirectories_77>
    <ProjectAdditionalIncludeDirectories_78>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ViewportInteraction\UHT</ProjectAdditionalIncludeDirectories_78>
    <ProjectAdditionalIncludeDirectories_79>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ViewportInteraction\VNI</ProjectAdditionalIncludeDirectories_79>
    <ProjectAdditionalIncludeDirectories_80>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Voronoi\UHT</ProjectAdditionalIncludeDirectories_80>
    <ProjectAdditionalIncludeDirectories_81>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Voronoi\VNI</ProjectAdditionalIncludeDirectories_81>
    <ProjectAdditionalIncludeDirectories_82>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WidgetRegistration\UHT</ProjectAdditionalIncludeDirectories_82>
    <ProjectAdditionalIncludeDirectories_83>C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WidgetRegistration\VNI</ProjectAdditionalIncludeDirectories_83>
    <ProjectAdditionalIncludeDirectories_84>C:\Program Files\Epic Games\UE_5.6\Engine\Shaders\Public</ProjectAdditionalIncludeDirectories_84>
    <ProjectAdditionalIncludeDirectories_85>C:\Program Files\Epic Games\UE_5.6\Engine\Shaders\Shared</ProjectAdditionalIncludeDirectories_85>
    <ProjectAdditionalIncludeDirectories_86>C:\Program Files\Epic Games\UE_5.6\Engine\Source</ProjectAdditionalIncludeDirectories_86>
    <ProjectAdditionalIncludeDirectories_87>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AnimationDataController\Public</ProjectAdditionalIncludeDirectories_87>
    <ProjectAdditionalIncludeDirectories_88>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AnimationWidgets\Public</ProjectAdditionalIncludeDirectories_88>
    <ProjectAdditionalIncludeDirectories_89>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AssetTools\Public</ProjectAdditionalIncludeDirectories_89>
    <ProjectAdditionalIncludeDirectories_90>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AutomationController\Public</ProjectAdditionalIncludeDirectories_90>
    <ProjectAdditionalIncludeDirectories_91>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CollectionManager\Public</ProjectAdditionalIncludeDirectories_91>
    <ProjectAdditionalIncludeDirectories_92>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DesktopPlatform\Public</ProjectAdditionalIncludeDirectories_92>
    <ProjectAdditionalIncludeDirectories_93>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeveloperToolSettings\Classes</ProjectAdditionalIncludeDirectories_93>
    <ProjectAdditionalIncludeDirectories_94>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeveloperToolSettings\Public</ProjectAdditionalIncludeDirectories_94>
    <ProjectAdditionalIncludeDirectories_95>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DirectoryWatcher\Public</ProjectAdditionalIncludeDirectories_95>
    <ProjectAdditionalIncludeDirectories_96>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\FunctionalTesting\Classes</ProjectAdditionalIncludeDirectories_96>
    <ProjectAdditionalIncludeDirectories_97>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\FunctionalTesting\Public</ProjectAdditionalIncludeDirectories_97>
    <ProjectAdditionalIncludeDirectories_98>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Horde\Public</ProjectAdditionalIncludeDirectories_98>
    <ProjectAdditionalIncludeDirectories_99>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Localization\Public</ProjectAdditionalIncludeDirectories_99>
    <ProjectAdditionalIncludeDirectories_100>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MaterialUtilities\Public</ProjectAdditionalIncludeDirectories_100>
    <ProjectAdditionalIncludeDirectories_101>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Merge\Public</ProjectAdditionalIncludeDirectories_101>
    <ProjectAdditionalIncludeDirectories_102>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshBuilder\Public</ProjectAdditionalIncludeDirectories_102>
    <ProjectAdditionalIncludeDirectories_103>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshMergeUtilities\Public</ProjectAdditionalIncludeDirectories_103>
    <ProjectAdditionalIncludeDirectories_104>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshReductionInterface\Public</ProjectAdditionalIncludeDirectories_104>
    <ProjectAdditionalIncludeDirectories_105>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshUtilities\Public</ProjectAdditionalIncludeDirectories_105>
    <ProjectAdditionalIncludeDirectories_106>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\PhysicsUtilities\Public</ProjectAdditionalIncludeDirectories_106>
    <ProjectAdditionalIncludeDirectories_107>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Settings\Public</ProjectAdditionalIncludeDirectories_107>
    <ProjectAdditionalIncludeDirectories_108>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SourceControl\Public</ProjectAdditionalIncludeDirectories_108>
    <ProjectAdditionalIncludeDirectories_109>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TargetPlatform\Public</ProjectAdditionalIncludeDirectories_109>
    <ProjectAdditionalIncludeDirectories_110>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureBuildUtilities\Public</ProjectAdditionalIncludeDirectories_110>
    <ProjectAdditionalIncludeDirectories_111>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureFormat\Public</ProjectAdditionalIncludeDirectories_111>
    <ProjectAdditionalIncludeDirectories_112>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ToolMenus\Public</ProjectAdditionalIncludeDirectories_112>
    <ProjectAdditionalIncludeDirectories_113>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ToolWidgets\Public</ProjectAdditionalIncludeDirectories_113>
    <ProjectAdditionalIncludeDirectories_114>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\UncontrolledChangelists\Public</ProjectAdditionalIncludeDirectories_114>
    <ProjectAdditionalIncludeDirectories_115>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Public</ProjectAdditionalIncludeDirectories_115>
    <ProjectAdditionalIncludeDirectories_116>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ActorPickerMode\Public</ProjectAdditionalIncludeDirectories_116>
    <ProjectAdditionalIncludeDirectories_117>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AdvancedPreviewScene\Public</ProjectAdditionalIncludeDirectories_117>
    <ProjectAdditionalIncludeDirectories_118>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationBlueprintEditor\Public</ProjectAdditionalIncludeDirectories_118>
    <ProjectAdditionalIncludeDirectories_119>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationEditMode\Public</ProjectAdditionalIncludeDirectories_119>
    <ProjectAdditionalIncludeDirectories_120>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationEditor\Public</ProjectAdditionalIncludeDirectories_120>
    <ProjectAdditionalIncludeDirectories_121>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AssetDefinition\Public</ProjectAdditionalIncludeDirectories_121>
    <ProjectAdditionalIncludeDirectories_122>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AssetTagsEditor\Public</ProjectAdditionalIncludeDirectories_122>
    <ProjectAdditionalIncludeDirectories_123>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AudioEditor\Classes</ProjectAdditionalIncludeDirectories_123>
    <ProjectAdditionalIncludeDirectories_124>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AudioEditor\Public</ProjectAdditionalIncludeDirectories_124>
    <ProjectAdditionalIncludeDirectories_125>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\BlueprintGraph\Classes</ProjectAdditionalIncludeDirectories_125>
    <ProjectAdditionalIncludeDirectories_126>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\BlueprintGraph\Public</ProjectAdditionalIncludeDirectories_126>
    <ProjectAdditionalIncludeDirectories_127>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ClassViewer\Public</ProjectAdditionalIncludeDirectories_127>
    <ProjectAdditionalIncludeDirectories_128>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CommonMenuExtensions\Public</ProjectAdditionalIncludeDirectories_128>
    <ProjectAdditionalIncludeDirectories_129>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ContentBrowserData\Public</ProjectAdditionalIncludeDirectories_129>
    <ProjectAdditionalIncludeDirectories_130>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ContentBrowser\Public</ProjectAdditionalIncludeDirectories_130>
    <ProjectAdditionalIncludeDirectories_131>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DetailCustomizations\Public</ProjectAdditionalIncludeDirectories_131>
    <ProjectAdditionalIncludeDirectories_132>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Documentation\Public</ProjectAdditionalIncludeDirectories_132>
    <ProjectAdditionalIncludeDirectories_133>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorConfig\Public</ProjectAdditionalIncludeDirectories_133>
    <ProjectAdditionalIncludeDirectories_134>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorFramework\Public</ProjectAdditionalIncludeDirectories_134>
    <ProjectAdditionalIncludeDirectories_135>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorSubsystem\Public</ProjectAdditionalIncludeDirectories_135>
    <ProjectAdditionalIncludeDirectories_136>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\GraphEditor\Public</ProjectAdditionalIncludeDirectories_136>
    <ProjectAdditionalIncludeDirectories_137>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\KismetCompiler\Public</ProjectAdditionalIncludeDirectories_137>
    <ProjectAdditionalIncludeDirectories_138>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Kismet\Classes</ProjectAdditionalIncludeDirectories_138>
    <ProjectAdditionalIncludeDirectories_139>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Kismet\Public</ProjectAdditionalIncludeDirectories_139>
    <ProjectAdditionalIncludeDirectories_140>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LevelEditor\Public</ProjectAdditionalIncludeDirectories_140>
    <ProjectAdditionalIncludeDirectories_141>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MainFrame\Public</ProjectAdditionalIncludeDirectories_141>
    <ProjectAdditionalIncludeDirectories_142>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MaterialEditor\Public</ProjectAdditionalIncludeDirectories_142>
    <ProjectAdditionalIncludeDirectories_143>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Persona\Public</ProjectAdditionalIncludeDirectories_143>
    <ProjectAdditionalIncludeDirectories_144>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Public</ProjectAdditionalIncludeDirectories_144>
    <ProjectAdditionalIncludeDirectories_145>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SceneDepthPickerMode\Public</ProjectAdditionalIncludeDirectories_145>
    <ProjectAdditionalIncludeDirectories_146>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SkeletonEditor\Public</ProjectAdditionalIncludeDirectories_146>
    <ProjectAdditionalIncludeDirectories_147>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StatusBar\Public</ProjectAdditionalIncludeDirectories_147>
    <ProjectAdditionalIncludeDirectories_148>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StructViewer\Public</ProjectAdditionalIncludeDirectories_148>
    <ProjectAdditionalIncludeDirectories_149>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SubobjectDataInterface\Public</ProjectAdditionalIncludeDirectories_149>
    <ProjectAdditionalIncludeDirectories_150>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SubobjectEditor\Public</ProjectAdditionalIncludeDirectories_150>
    <ProjectAdditionalIncludeDirectories_151>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ToolMenusEditor\Public</ProjectAdditionalIncludeDirectories_151>
    <ProjectAdditionalIncludeDirectories_152>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEdMessages\Classes</ProjectAdditionalIncludeDirectories_152>
    <ProjectAdditionalIncludeDirectories_153>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEdMessages\Public</ProjectAdditionalIncludeDirectories_153>
    <ProjectAdditionalIncludeDirectories_154>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Classes</ProjectAdditionalIncludeDirectories_154>
    <ProjectAdditionalIncludeDirectories_155>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Public</ProjectAdditionalIncludeDirectories_155>
    <ProjectAdditionalIncludeDirectories_156>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\VREditor\Public</ProjectAdditionalIncludeDirectories_156>
    <ProjectAdditionalIncludeDirectories_157>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ViewportInteraction\Public</ProjectAdditionalIncludeDirectories_157>
    <ProjectAdditionalIncludeDirectories_158>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Programs\UnrealLightmass\Public</ProjectAdditionalIncludeDirectories_158>
    <ProjectAdditionalIncludeDirectories_159>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\AnalyticsET\Public</ProjectAdditionalIncludeDirectories_159>
    <ProjectAdditionalIncludeDirectories_160>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\Analytics\Public</ProjectAdditionalIncludeDirectories_160>
    <ProjectAdditionalIncludeDirectories_161>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AnimationCore\Public</ProjectAdditionalIncludeDirectories_161>
    <ProjectAdditionalIncludeDirectories_162>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Public</ProjectAdditionalIncludeDirectories_162>
    <ProjectAdditionalIncludeDirectories_163>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AssetRegistry\Public</ProjectAdditionalIncludeDirectories_163>
    <ProjectAdditionalIncludeDirectories_164>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioExtensions\Public</ProjectAdditionalIncludeDirectories_164>
    <ProjectAdditionalIncludeDirectories_165>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioLink\AudioLinkCore\Public</ProjectAdditionalIncludeDirectories_165>
    <ProjectAdditionalIncludeDirectories_166>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioLink\AudioLinkEngine\Public</ProjectAdditionalIncludeDirectories_166>
    <ProjectAdditionalIncludeDirectories_167>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixerCore\Public</ProjectAdditionalIncludeDirectories_167>
    <ProjectAdditionalIncludeDirectories_168>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixer\Classes</ProjectAdditionalIncludeDirectories_168>
    <ProjectAdditionalIncludeDirectories_169>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixer\Public</ProjectAdditionalIncludeDirectories_169>
    <ProjectAdditionalIncludeDirectories_170>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioPlatformConfiguration\Public</ProjectAdditionalIncludeDirectories_170>
    <ProjectAdditionalIncludeDirectories_171>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AutoRTFM\Public</ProjectAdditionalIncludeDirectories_171>
    <ProjectAdditionalIncludeDirectories_172>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AutomationTest\Public</ProjectAdditionalIncludeDirectories_172>
    <ProjectAdditionalIncludeDirectories_173>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ClothingSystemRuntimeInterface\Public</ProjectAdditionalIncludeDirectories_173>
    <ProjectAdditionalIncludeDirectories_174>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreOnline\Public</ProjectAdditionalIncludeDirectories_174>
    <ProjectAdditionalIncludeDirectories_175>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CorePreciseFP\Public</ProjectAdditionalIncludeDirectories_175>
    <ProjectAdditionalIncludeDirectories_176>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Public</ProjectAdditionalIncludeDirectories_176>
    <ProjectAdditionalIncludeDirectories_177>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Public</ProjectAdditionalIncludeDirectories_177>
    <ProjectAdditionalIncludeDirectories_178>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\DeveloperSettings\Public</ProjectAdditionalIncludeDirectories_178>
    <ProjectAdditionalIncludeDirectories_179>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\EngineMessages\Public</ProjectAdditionalIncludeDirectories_179>
    <ProjectAdditionalIncludeDirectories_180>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\EngineSettings\Classes</ProjectAdditionalIncludeDirectories_180>
    <ProjectAdditionalIncludeDirectories_181>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\EngineSettings\Public</ProjectAdditionalIncludeDirectories_181>
    <ProjectAdditionalIncludeDirectories_182>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Classes</ProjectAdditionalIncludeDirectories_182>
    <ProjectAdditionalIncludeDirectories_183>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Public</ProjectAdditionalIncludeDirectories_183>
    <ProjectAdditionalIncludeDirectories_184>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Animation\Constraints\Public</ProjectAdditionalIncludeDirectories_184>
    <ProjectAdditionalIncludeDirectories_185>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosCore\Public</ProjectAdditionalIncludeDirectories_185>
    <ProjectAdditionalIncludeDirectories_186>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosSolverEngine\Public</ProjectAdditionalIncludeDirectories_186>
    <ProjectAdditionalIncludeDirectories_187>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosVisualDebugger\Public</ProjectAdditionalIncludeDirectories_187>
    <ProjectAdditionalIncludeDirectories_188>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Public</ProjectAdditionalIncludeDirectories_188>
    <ProjectAdditionalIncludeDirectories_189>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Core\Public</ProjectAdditionalIncludeDirectories_189>
    <ProjectAdditionalIncludeDirectories_190>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Engine\Public</ProjectAdditionalIncludeDirectories_190>
    <ProjectAdditionalIncludeDirectories_191>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Simulation\Public</ProjectAdditionalIncludeDirectories_191>
    <ProjectAdditionalIncludeDirectories_192>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\FieldSystem\Source\FieldSystemEngine\Public</ProjectAdditionalIncludeDirectories_192>
    <ProjectAdditionalIncludeDirectories_193>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Public</ProjectAdditionalIncludeDirectories_193>
    <ProjectAdditionalIncludeDirectories_194>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ISMPool\Public</ProjectAdditionalIncludeDirectories_194>
    <ProjectAdditionalIncludeDirectories_195>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\IoStore\OnDemandCore\Public</ProjectAdditionalIncludeDirectories_195>
    <ProjectAdditionalIncludeDirectories_196>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Public</ProjectAdditionalIncludeDirectories_196>
    <ProjectAdditionalIncludeDirectories_197>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Voronoi\Public</ProjectAdditionalIncludeDirectories_197>
    <ProjectAdditionalIncludeDirectories_198>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\FieldNotification\Public</ProjectAdditionalIncludeDirectories_198>
    <ProjectAdditionalIncludeDirectories_199>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayTags\Classes</ProjectAdditionalIncludeDirectories_199>
    <ProjectAdditionalIncludeDirectories_200>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayTags\Public</ProjectAdditionalIncludeDirectories_200>
    <ProjectAdditionalIncludeDirectories_201>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayTasks\Classes</ProjectAdditionalIncludeDirectories_201>
    <ProjectAdditionalIncludeDirectories_202>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayTasks\Public</ProjectAdditionalIncludeDirectories_202>
    <ProjectAdditionalIncludeDirectories_203>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Public</ProjectAdditionalIncludeDirectories_203>
    <ProjectAdditionalIncludeDirectories_204>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\HeadMountedDisplay\Public</ProjectAdditionalIncludeDirectories_204>
    <ProjectAdditionalIncludeDirectories_205>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ImageCore\Public</ProjectAdditionalIncludeDirectories_205>
    <ProjectAdditionalIncludeDirectories_206>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ImageWrapper\Public</ProjectAdditionalIncludeDirectories_206>
    <ProjectAdditionalIncludeDirectories_207>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputCore\Classes</ProjectAdditionalIncludeDirectories_207>
    <ProjectAdditionalIncludeDirectories_208>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputCore\Public</ProjectAdditionalIncludeDirectories_208>
    <ProjectAdditionalIncludeDirectories_209>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Public</ProjectAdditionalIncludeDirectories_209>
    <ProjectAdditionalIncludeDirectories_210>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Interchange\Core\Public</ProjectAdditionalIncludeDirectories_210>
    <ProjectAdditionalIncludeDirectories_211>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Interchange\Engine\Public</ProjectAdditionalIncludeDirectories_211>
    <ProjectAdditionalIncludeDirectories_212>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\JsonUtilities\Public</ProjectAdditionalIncludeDirectories_212>
    <ProjectAdditionalIncludeDirectories_213>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Json\Public</ProjectAdditionalIncludeDirectories_213>
    <ProjectAdditionalIncludeDirectories_214>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Landscape\Classes</ProjectAdditionalIncludeDirectories_214>
    <ProjectAdditionalIncludeDirectories_215>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Landscape\Public</ProjectAdditionalIncludeDirectories_215>
    <ProjectAdditionalIncludeDirectories_216>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MaterialShaderQualitySettings\Classes</ProjectAdditionalIncludeDirectories_216>
    <ProjectAdditionalIncludeDirectories_217>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MeshDescription\Public</ProjectAdditionalIncludeDirectories_217>
    <ProjectAdditionalIncludeDirectories_218>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MeshUtilitiesCommon\Public</ProjectAdditionalIncludeDirectories_218>
    <ProjectAdditionalIncludeDirectories_219>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MessagingCommon\Public</ProjectAdditionalIncludeDirectories_219>
    <ProjectAdditionalIncludeDirectories_220>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Messaging\Public</ProjectAdditionalIncludeDirectories_220>
    <ProjectAdditionalIncludeDirectories_221>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneCapture\Public</ProjectAdditionalIncludeDirectories_221>
    <ProjectAdditionalIncludeDirectories_222>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Public</ProjectAdditionalIncludeDirectories_222>
    <ProjectAdditionalIncludeDirectories_223>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Public</ProjectAdditionalIncludeDirectories_223>
    <ProjectAdditionalIncludeDirectories_224>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NNE\Public</ProjectAdditionalIncludeDirectories_224>
    <ProjectAdditionalIncludeDirectories_225>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NavigationSystem\Public</ProjectAdditionalIncludeDirectories_225>
    <ProjectAdditionalIncludeDirectories_226>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Common\Public</ProjectAdditionalIncludeDirectories_226>
    <ProjectAdditionalIncludeDirectories_227>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Classes</ProjectAdditionalIncludeDirectories_227>
    <ProjectAdditionalIncludeDirectories_228>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Public</ProjectAdditionalIncludeDirectories_228>
    <ProjectAdditionalIncludeDirectories_229>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkFileSystem\Public</ProjectAdditionalIncludeDirectories_229>
    <ProjectAdditionalIncludeDirectories_230>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkReplayStreaming\NetworkReplayStreaming\Public</ProjectAdditionalIncludeDirectories_230>
    <ProjectAdditionalIncludeDirectories_231>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Networking\Public</ProjectAdditionalIncludeDirectories_231>
    <ProjectAdditionalIncludeDirectories_232>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Public</ProjectAdditionalIncludeDirectories_232>
    <ProjectAdditionalIncludeDirectories_233>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\OpenGLDrv\Public</ProjectAdditionalIncludeDirectories_233>
    <ProjectAdditionalIncludeDirectories_234>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PacketHandlers\PacketHandler\Classes</ProjectAdditionalIncludeDirectories_234>
    <ProjectAdditionalIncludeDirectories_235>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PacketHandlers\PacketHandler\Public</ProjectAdditionalIncludeDirectories_235>
    <ProjectAdditionalIncludeDirectories_236>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PacketHandlers\ReliabilityHandlerComponent\Public</ProjectAdditionalIncludeDirectories_236>
    <ProjectAdditionalIncludeDirectories_237>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PakFile\Public</ProjectAdditionalIncludeDirectories_237>
    <ProjectAdditionalIncludeDirectories_238>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PhysicsCore\Public</ProjectAdditionalIncludeDirectories_238>
    <ProjectAdditionalIncludeDirectories_239>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Projects\Public</ProjectAdditionalIncludeDirectories_239>
    <ProjectAdditionalIncludeDirectories_240>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PropertyPath\Public</ProjectAdditionalIncludeDirectories_240>
    <ProjectAdditionalIncludeDirectories_241>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RHI\Public</ProjectAdditionalIncludeDirectories_241>
    <ProjectAdditionalIncludeDirectories_242>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RSA\Public</ProjectAdditionalIncludeDirectories_242>
    <ProjectAdditionalIncludeDirectories_243>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RawMesh\Public</ProjectAdditionalIncludeDirectories_243>
    <ProjectAdditionalIncludeDirectories_244>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RenderCore\Public</ProjectAdditionalIncludeDirectories_244>
    <ProjectAdditionalIncludeDirectories_245>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Public</ProjectAdditionalIncludeDirectories_245>
    <ProjectAdditionalIncludeDirectories_246>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SandboxFile\Public</ProjectAdditionalIncludeDirectories_246>
    <ProjectAdditionalIncludeDirectories_247>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SignalProcessing\Public</ProjectAdditionalIncludeDirectories_247>
    <ProjectAdditionalIncludeDirectories_248>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SkeletalMeshDescription\Public</ProjectAdditionalIncludeDirectories_248>
    <ProjectAdditionalIncludeDirectories_249>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Public</ProjectAdditionalIncludeDirectories_249>
    <ProjectAdditionalIncludeDirectories_250>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Public</ProjectAdditionalIncludeDirectories_250>
    <ProjectAdditionalIncludeDirectories_251>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Sockets\Public</ProjectAdditionalIncludeDirectories_251>
    <ProjectAdditionalIncludeDirectories_252>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StateStream\Public</ProjectAdditionalIncludeDirectories_252>
    <ProjectAdditionalIncludeDirectories_253>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StaticMeshDescription\Public</ProjectAdditionalIncludeDirectories_253>
    <ProjectAdditionalIncludeDirectories_254>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SynthBenchmark\Public</ProjectAdditionalIncludeDirectories_254>
    <ProjectAdditionalIncludeDirectories_255>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TimeManagement\Public</ProjectAdditionalIncludeDirectories_255>
    <ProjectAdditionalIncludeDirectories_256>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TraceLog\Public</ProjectAdditionalIncludeDirectories_256>
    <ProjectAdditionalIncludeDirectories_257>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Public</ProjectAdditionalIncludeDirectories_257>
    <ProjectAdditionalIncludeDirectories_258>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Tests</ProjectAdditionalIncludeDirectories_258>
    <ProjectAdditionalIncludeDirectories_259>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementRuntime\Public</ProjectAdditionalIncludeDirectories_259>
    <ProjectAdditionalIncludeDirectories_260>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Public</ProjectAdditionalIncludeDirectories_260>
    <ProjectAdditionalIncludeDirectories_261>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UniversalObjectLocator\Public</ProjectAdditionalIncludeDirectories_261>
    <ProjectAdditionalIncludeDirectories_262>C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\AtomicQueue</ProjectAdditionalIncludeDirectories_262>
    <ProjectAdditionalIncludeDirectories_263>C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\GuidelinesSupportLibrary\GSL-1144\include</ProjectAdditionalIncludeDirectories_263>
    <ProjectAdditionalIncludeDirectories_264>C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\LibTiff\Source</ProjectAdditionalIncludeDirectories_264>
    <ProjectAdditionalIncludeDirectories_265>C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\LibTiff\Source\Win64</ProjectAdditionalIncludeDirectories_265>
    <ProjectAdditionalIncludeDirectories_266>C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\OpenGL</ProjectAdditionalIncludeDirectories_266>
    <ProjectAdditionalIncludeDirectories_267>C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\RapidJSON\1.1.0</ProjectAdditionalIncludeDirectories_267>
    <ProjectAdditionalIncludeDirectories_268>C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libSampleRate\Public</ProjectAdditionalIncludeDirectories_268>
    <ClCompile_AdditionalIncludeDirectories>$(NMakeIncludeSearchPath);$(ProjectAdditionalIncludeDirectories_86);..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private;$(ProjectAdditionalIncludeDirectories_74);$(ProjectAdditionalIncludeDirectories_75);$(ProjectAdditionalIncludeDirectories_158);$(ProjectAdditionalIncludeDirectories_154);$(ProjectAdditionalIncludeDirectories_155);$(ProjectAdditionalIncludeDirectories_121);$(ProjectAdditionalIncludeDirectories_177);$(ProjectAdditionalIncludeDirectories_58);$(ProjectAdditionalIncludeDirectories_59);$(ProjectAdditionalIncludeDirectories_256);$(ProjectAdditionalIncludeDirectories_171);$(ProjectAdditionalIncludeDirectories_205);$(ProjectAdditionalIncludeDirectories_176);$(ProjectAdditionalIncludeDirectories_175);$(ProjectAdditionalIncludeDirectories_163);$(ProjectAdditionalIncludeDirectories_182);$(ProjectAdditionalIncludeDirectories_183);$(ProjectAdditionalIncludeDirectories_174);$(ProjectAdditionalIncludeDirectories_198);$(ProjectAdditionalIncludeDirectories_227);$(ProjectAdditionalIncludeDirectories_228);$(ProjectAdditionalIncludeDirectories_226);$(ProjectAdditionalIncludeDirectories_213);$(ProjectAdditionalIncludeDirectories_212);$(ProjectAdditionalIncludeDirectories_22);$(ProjectAdditionalIncludeDirectories_23);$(ProjectAdditionalIncludeDirectories_249);$(ProjectAdditionalIncludeDirectories_178);$(ProjectAdditionalIncludeDirectories_207);$(ProjectAdditionalIncludeDirectories_208);$(ProjectAdditionalIncludeDirectories_162);$(ProjectAdditionalIncludeDirectories);$(ProjectAdditionalIncludeDirectories_1);$(ProjectAdditionalIncludeDirectories_241);$(ProjectAdditionalIncludeDirectories_24);$(ProjectAdditionalIncludeDirectories_25);$(ProjectAdditionalIncludeDirectories_250);$(ProjectAdditionalIncludeDirectories_206);$(ProjectAdditionalIncludeDirectories_220);$(ProjectAdditionalIncludeDirectories_219);$(ProjectAdditionalIncludeDirectories_6);$(ProjectAdditionalIncludeDirectories_7);$(ProjectAdditionalIncludeDirectories_244);$(ProjectAdditionalIncludeDirectories_233);$(ProjectAdditionalIncludeDirectories_159);$(ProjectAdditionalIncludeDirectories_160);$(ProjectAdditionalIncludeDirectories_26);$(ProjectAdditionalIncludeDirectories_27);$(ProjectAdditionalIncludeDirectories_251);$(ProjectAdditionalIncludeDirectories_179);$(ProjectAdditionalIncludeDirectories_180);$(ProjectAdditionalIncludeDirectories_181);$(ProjectAdditionalIncludeDirectories_42);$(ProjectAdditionalIncludeDirectories_43);$(ProjectAdditionalIncludeDirectories_254);$(ProjectAdditionalIncludeDirectories_199);$(ProjectAdditionalIncludeDirectories_200);$(ProjectAdditionalIncludeDirectories_234);$(ProjectAdditionalIncludeDirectories_235);$(ProjectAdditionalIncludeDirectories_4);$(ProjectAdditionalIncludeDirectories_5);$(ProjectAdditionalIncludeDirectories_236);$(ProjectAdditionalIncludeDirectories_170);$(ProjectAdditionalIncludeDirectories_217);$(ProjectAdditionalIncludeDirectories_32);$(ProjectAdditionalIncludeDirectories_33);$(ProjectAdditionalIncludeDirectories_253);$(ProjectAdditionalIncludeDirectories_18);$(ProjectAdditionalIncludeDirectories_19);$(ProjectAdditionalIncludeDirectories_248);$(ProjectAdditionalIncludeDirectories_161);$(ProjectAdditionalIncludeDirectories_237);$(ProjectAdditionalIncludeDirectories_2);$(ProjectAdditionalIncludeDirectories_3);$(ProjectAdditionalIncludeDirectories_242);$(ProjectAdditionalIncludeDirectories_230);$(ProjectAdditionalIncludeDirectories_238);$(ProjectAdditionalIncludeDirectories_185);$(ProjectAdditionalIncludeDirectories_188);$(ProjectAdditionalIncludeDirectories_80);$(ProjectAdditionalIncludeDirectories_81);$(ProjectAdditionalIncludeDirectories_197);$(ProjectAdditionalIncludeDirectories_203);$(ProjectAdditionalIncludeDirectories_187);$(ProjectAdditionalIncludeDirectories_224);$(ProjectAdditionalIncludeDirectories_16);$(ProjectAdditionalIncludeDirectories_17);$(ProjectAdditionalIncludeDirectories_247);$(ProjectAdditionalIncludeDirectories_30);$(ProjectAdditionalIncludeDirectories_31);$(ProjectAdditionalIncludeDirectories_252);$(ProjectAdditionalIncludeDirectories_164);$(ProjectAdditionalIncludeDirectories_167);$(ProjectAdditionalIncludeDirectories_168);$(ProjectAdditionalIncludeDirectories_169);$(ProjectAdditionalIncludeDirectories_44);$(ProjectAdditionalIncludeDirectories_45);$(ProjectAdditionalIncludeDirectories_109);$(ProjectAdditionalIncludeDirectories_48);$(ProjectAdditionalIncludeDirectories_49);$(ProjectAdditionalIncludeDirectories_111);$(ProjectAdditionalIncludeDirectories_92);$(ProjectAdditionalIncludeDirectories_166);$(ProjectAdditionalIncludeDirectories_165);$(ProjectAdditionalIncludeDirectories_231);$(ProjectAdditionalIncludeDirectories_195);$(ProjectAdditionalIncludeDirectories_46);$(ProjectAdditionalIncludeDirectories_47);$(ProjectAdditionalIncludeDirectories_110);$(ProjectAdditionalIncludeDirectories_98);$(ProjectAdditionalIncludeDirectories_173);$(ProjectAdditionalIncludeDirectories_196);$(ProjectAdditionalIncludeDirectories_221);$(ProjectAdditionalIncludeDirectories_8);$(ProjectAdditionalIncludeDirectories_9);$(ProjectAdditionalIncludeDirectories_245);$(ProjectAdditionalIncludeDirectories_84);$(ProjectAdditionalIncludeDirectories_85);$(ProjectAdditionalIncludeDirectories_60);$(ProjectAdditionalIncludeDirectories_61);$(ProjectAdditionalIncludeDirectories_258);$(ProjectAdditionalIncludeDirectories_257);$(ProjectAdditionalIncludeDirectories_62);$(ProjectAdditionalIncludeDirectories_63);$(ProjectAdditionalIncludeDirectories_259);$(ProjectAdditionalIncludeDirectories_87);$(ProjectAdditionalIncludeDirectories_118);$(ProjectAdditionalIncludeDirectories_138);$(ProjectAdditionalIncludeDirectories_139);$(ProjectAdditionalIncludeDirectories_143);$(ProjectAdditionalIncludeDirectories_20);$(ProjectAdditionalIncludeDirectories_21);$(ProjectAdditionalIncludeDirectories_146);$(ProjectAdditionalIncludeDirectories_88);$(ProjectAdditionalIncludeDirectories_56);$(ProjectAdditionalIncludeDirectories_57);$(ProjectAdditionalIncludeDirectories_113);$(ProjectAdditionalIncludeDirectories_54);$(ProjectAdditionalIncludeDirectories_55);$(ProjectAdditionalIncludeDirectories_112);$(ProjectAdditionalIncludeDirectories_120);$(ProjectAdditionalIncludeDirectories_117);$(ProjectAdditionalIncludeDirectories_144);$(ProjectAdditionalIncludeDirectories_133);$(ProjectAdditionalIncludeDirectories_134);$(ProjectAdditionalIncludeDirectories_135);$(ProjectAdditionalIncludeDirectories_209);$(ProjectAdditionalIncludeDirectories_116);$(ProjectAdditionalIncludeDirectories_12);$(ProjectAdditionalIncludeDirectories_13);$(ProjectAdditionalIncludeDirectories_145);$(ProjectAdditionalIncludeDirectories_119);$(ProjectAdditionalIncludeDirectories_210);$(ProjectAdditionalIncludeDirectories_95);$(ProjectAdditionalIncludeDirectories_132);$(ProjectAdditionalIncludeDirectories_141);$(ProjectAdditionalIncludeDirectories_239);$(ProjectAdditionalIncludeDirectories_10);$(ProjectAdditionalIncludeDirectories_11);$(ProjectAdditionalIncludeDirectories_246);$(ProjectAdditionalIncludeDirectories_28);$(ProjectAdditionalIncludeDirectories_29);$(ProjectAdditionalIncludeDirectories_108);$(ProjectAdditionalIncludeDirectories_68);$(ProjectAdditionalIncludeDirectories_69);$(ProjectAdditionalIncludeDirectories_114);$(ProjectAdditionalIncludeDirectories_72);$(ProjectAdditionalIncludeDirectories_73);$(ProjectAdditionalIncludeDirectories_152);$(ProjectAdditionalIncludeDirectories_153);$(ProjectAdditionalIncludeDirectories_125);$(ProjectAdditionalIncludeDirectories_126);$(ProjectAdditionalIncludeDirectories_232);$(ProjectAdditionalIncludeDirectories_96);$(ProjectAdditionalIncludeDirectories_97);$(ProjectAdditionalIncludeDirectories_90);$(ProjectAdditionalIncludeDirectories_172);$(ProjectAdditionalIncludeDirectories_99);$(ProjectAdditionalIncludeDirectories_123);$(ProjectAdditionalIncludeDirectories_124);$(ProjectAdditionalIncludeDirectories_140);$(ProjectAdditionalIncludeDirectories_128);$(ProjectAdditionalIncludeDirectories_14);$(ProjectAdditionalIncludeDirectories_15);$(ProjectAdditionalIncludeDirectories_107);$(ProjectAdditionalIncludeDirectories_76);$(ProjectAdditionalIncludeDirectories_77);$(ProjectAdditionalIncludeDirectories_156);$(ProjectAdditionalIncludeDirectories_78);$(ProjectAdditionalIncludeDirectories_79);$(ProjectAdditionalIncludeDirectories_157);$(ProjectAdditionalIncludeDirectories_204);$(ProjectAdditionalIncludeDirectories_214);$(ProjectAdditionalIncludeDirectories_215);$(ProjectAdditionalIncludeDirectories_131);$(ProjectAdditionalIncludeDirectories_127);$(ProjectAdditionalIncludeDirectories_136);$(ProjectAdditionalIncludeDirectories_36);$(ProjectAdditionalIncludeDirectories_37);$(ProjectAdditionalIncludeDirectories_148);$(ProjectAdditionalIncludeDirectories_142);$(ProjectAdditionalIncludeDirectories_130);$(ProjectAdditionalIncludeDirectories_89);$(ProjectAdditionalIncludeDirectories_101);$(ProjectAdditionalIncludeDirectories_91);$(ProjectAdditionalIncludeDirectories_129);$(ProjectAdditionalIncludeDirectories_64);$(ProjectAdditionalIncludeDirectories_65);$(ProjectAdditionalIncludeDirectories_268);$(ProjectAdditionalIncludeDirectories_229);$(ProjectAdditionalIncludeDirectories_66);$(ProjectAdditionalIncludeDirectories_67);$(ProjectAdditionalIncludeDirectories_260);$(ProjectAdditionalIncludeDirectories_223);$(ProjectAdditionalIncludeDirectories_50);$(ProjectAdditionalIncludeDirectories_51);$(ProjectAdditionalIncludeDirectories_255);$(ProjectAdditionalIncludeDirectories_70);$(ProjectAdditionalIncludeDirectories_71);$(ProjectAdditionalIncludeDirectories_261);$(ProjectAdditionalIncludeDirectories_222);$(ProjectAdditionalIncludeDirectories_184);$(ProjectAdditionalIncludeDirectories_240);$(ProjectAdditionalIncludeDirectories_225);$(ProjectAdditionalIncludeDirectories_193);$(ProjectAdditionalIncludeDirectories_186);$(ProjectAdditionalIncludeDirectories_189);$(ProjectAdditionalIncludeDirectories_190);$(ProjectAdditionalIncludeDirectories_191);$(ProjectAdditionalIncludeDirectories_192);$(ProjectAdditionalIncludeDirectories_194);$(ProjectAdditionalIncludeDirectories_102);$(ProjectAdditionalIncludeDirectories_218);$(ProjectAdditionalIncludeDirectories_216);$(ProjectAdditionalIncludeDirectories_52);$(ProjectAdditionalIncludeDirectories_53);$(ProjectAdditionalIncludeDirectories_151);$(ProjectAdditionalIncludeDirectories_34);$(ProjectAdditionalIncludeDirectories_35);$(ProjectAdditionalIncludeDirectories_147);$(ProjectAdditionalIncludeDirectories_211);$(ProjectAdditionalIncludeDirectories_93);$(ProjectAdditionalIncludeDirectories_94);$(ProjectAdditionalIncludeDirectories_38);$(ProjectAdditionalIncludeDirectories_39);$(ProjectAdditionalIncludeDirectories_149);$(ProjectAdditionalIncludeDirectories_40);$(ProjectAdditionalIncludeDirectories_41);$(ProjectAdditionalIncludeDirectories_150);$(ProjectAdditionalIncludeDirectories_106);$(ProjectAdditionalIncludeDirectories_82);$(ProjectAdditionalIncludeDirectories_83);$(ProjectAdditionalIncludeDirectories_115);$(ProjectAdditionalIncludeDirectories_201);$(ProjectAdditionalIncludeDirectories_202);$(ProjectAdditionalIncludeDirectories_122);$(ProjectAdditionalIncludeDirectories_105);$(ProjectAdditionalIncludeDirectories_103);$(ProjectAdditionalIncludeDirectories_104);$(ProjectAdditionalIncludeDirectories_243);$(ProjectAdditionalIncludeDirectories_100);$(ProjectAdditionalIncludeDirectories_137);C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Editor\EditorScriptingUtilities\Intermediate\Build\Win64\UnrealEditor\Inc\EditorScriptingUtilities\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Editor\EditorScriptingUtilities\Intermediate\Build\Win64\UnrealEditor\Inc\EditorScriptingUtilities\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Editor\EditorScriptingUtilities\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Editor\EditorScriptingUtilities\Source\EditorScriptingUtilities\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StaticMeshEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StaticMeshEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BlueprintEditorLibrary\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\BlueprintEditorLibrary\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\BlueprintEditorLibrary\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UMGEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\UMGEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Classes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SequencerCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SequencerCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CurveEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\CurveEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SequencerWidgets\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SequencerWidgets\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerWidgets\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Sequencer\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Sequencer\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneOutliner\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SceneOutliner\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SceneOutliner\Public;..\..\Plugins\UnrealMCP\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealMCP\UHT;..\..\Plugins\UnrealMCP\Intermediate\Build\Win64\UnrealEditor\Inc\UnrealMCP\VNI;..\..\Plugins\UnrealMCP\Source;..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\Niagara\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\Niagara\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Source\Niagara\Classes;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Source\Niagara\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\NiagaraCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\NiagaraCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Source\NiagaraCore\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VectorVM\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\VectorVM\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VectorVM\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\NiagaraShader\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\NiagaraShader\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Shaders\Shared;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Source\NiagaraShader\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\NiagaraVertexFactories\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\NiagaraVertexFactories\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Source\NiagaraVertexFactories\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\NiagaraEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Intermediate\Build\Win64\UnrealEditor\Inc\NiagaraEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Source\NiagaraEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AppFramework\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AppFramework\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AppFramework\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataHierarchyEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\DataHierarchyEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DataHierarchyEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorWidgets\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorWidgets\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorWidgets\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Foliage\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Foliage\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Foliage\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AIModule\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AIModule\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Classes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MassEntity\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MassEntity\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MassEntity\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassMovement\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassMovement\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source\MassMovement\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassCommon\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassCommon\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source\MassCommon\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassLOD\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassLOD\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source\MassLOD\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassSimulation\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassSimulation\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source\MassSimulation\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassSpawner\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassSpawner\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source\MassSpawner\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraph\Intermediate\Build\Win64\UnrealEditor\Inc\ZoneGraph\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraph\Intermediate\Build\Win64\UnrealEditor\Inc\ZoneGraph\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraph\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraph\Source\ZoneGraph\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MassEntityEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MassEntityEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MassEntityEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ComponentVisualizers\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ComponentVisualizers\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ComponentVisualizers\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassSignals\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassSignals\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source\MassSignals\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraphAnnotations\Intermediate\Build\Win64\UnrealEditor\Inc\ZoneGraphAnnotations\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraphAnnotations\Intermediate\Build\Win64\UnrealEditor\Inc\ZoneGraphAnnotations\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraphAnnotations\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraphAnnotations\Source\ZoneGraphAnnotations\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraph\Intermediate\Build\Win64\UnrealEditor\Inc\ZoneGraphDebug\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraph\Intermediate\Build\Win64\UnrealEditor\Inc\ZoneGraphDebug\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ZoneGraph\Source\ZoneGraphDebug\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassRepresentation\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassRepresentation\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source\MassRepresentation\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassActors\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassActors\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source\MassActors\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassReplication\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Intermediate\Build\Win64\UnrealEditor\Inc\MassReplication\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MassGameplay\Source\MassReplication\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\StateTree\Intermediate\Build\Win64\UnrealEditor\Inc\StateTreeModule\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\StateTree\Intermediate\Build\Win64\UnrealEditor\Inc\StateTreeModule\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\StateTree\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\StateTree\Source\StateTreeModule\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\PropertyBindingUtils\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyBindingUtils\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\PropertyBindingUtils\Intermediate\Build\Win64\UnrealEditor\Inc\PropertyBindingUtils\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\PropertyBindingUtils\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\PropertyBindingUtils\Source\PropertyBindingUtils\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceServices\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceServices\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceServices\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Cbor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Cbor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Cbor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceAnalysis\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TraceAnalysis\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceAnalysis\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GameplayAbilities\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayAbilities\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GameplayAbilities\Intermediate\Build\Win64\UnrealEditor\Inc\GameplayAbilities\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GameplayAbilities\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GameplayAbilities\Source\GameplayAbilities\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\DataRegistry\Intermediate\Build\Win64\UnrealEditor\Inc\DataRegistry\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\DataRegistry\Intermediate\Build\Win64\UnrealEditor\Inc\DataRegistry\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\DataRegistry\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\DataRegistry\Source\DataRegistry\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\PCG\Intermediate\Build\Win64\UnrealEditor\Inc\PCG\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\PCG\Intermediate\Build\Win64\UnrealEditor\Inc\PCG\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\PCG\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\PCG\Source\PCG\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ComputeFramework\Intermediate\Build\Win64\UnrealEditor\Inc\ComputeFramework\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ComputeFramework\Intermediate\Build\Win64\UnrealEditor\Inc\ComputeFramework\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ComputeFramework\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ComputeFramework\Source\ComputeFramework\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemRuntimeCommon\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ClothingSystemRuntimeCommon\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ClothingSystemRuntimeCommon\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\GeometryCollectionPlugin\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCollectionEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\GeometryCollectionPlugin\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCollectionEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\GeometryCollectionPlugin\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\GeometryCollectionPlugin\Source\GeometryCollectionEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorStyle\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorStyle\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorStyle\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Source\DataflowEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\CharacterFXEditor\BaseCharacterFXEditor\Intermediate\Build\Win64\UnrealEditor\Inc\BCFXEd\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\CharacterFXEditor\BaseCharacterFXEditor\Intermediate\Build\Win64\UnrealEditor\Inc\BCFXEd\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\CharacterFXEditor\BaseCharacterFXEditor\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\CharacterFXEditor\BaseCharacterFXEditor\Source\BaseCharacterFXEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowAssetTools\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowAssetTools\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Source\DataflowAssetTools\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshConversion\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MeshConversion\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MeshConversion\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEnginePlugin\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowEnginePlugin\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Source\DataflowEnginePlugin\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryFramework\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryFramework\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryFramework\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowNodes\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Intermediate\Build\Win64\UnrealEditor\Inc\DataflowNodes\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Dataflow\Source\DataflowNodes\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryProcessing\Intermediate\Build\Win64\UnrealEditor\Inc\DynamicMesh\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryProcessing\Intermediate\Build\Win64\UnrealEditor\Inc\DynamicMesh\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryProcessing\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryProcessing\Source\DynamicMesh\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryProcessing\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryAlgorithms\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryProcessing\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryAlgorithms\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryProcessing\Source\GeometryAlgorithms\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorInteractiveToolsFramework\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\EditorInteractiveToolsFramework\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCache\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCache\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Source\GeometryCache\Classes;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Source\GeometryCache\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCacheEd\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Intermediate\Build\Win64\UnrealEditor\Inc\GeometryCacheEd\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Source\GeometryCacheEd\Classes;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Source\GeometryCacheEd\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\MeshModelingTools\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\MeshModelingTools\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Source\MeshModelingTools\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\ModelingComponents\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\ModelingComponents\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Source\ModelingComponents\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\ModelingOperators\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\ModelingOperators\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Source\ModelingOperators\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureUtilitiesCommon\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\TextureUtilitiesCommon\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TextureUtilitiesCommon\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\MeshModelingToolsEditorOnly\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\MeshModelingToolsEditorOnly\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Source\MeshModelingToolsEditorOnly\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\ModelingComponentsEditorOnly\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\ModelingComponentsEditorOnly\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Source\ModelingComponentsEditorOnly\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\ModelingOperatorsEditorOnly\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\ModelingOperatorsEditorOnly\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Source\ModelingOperatorsEditorOnly\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshUtilitiesCommon\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshUtilitiesCommon\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SkeletalMeshUtilitiesCommon\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshModifiers\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Intermediate\Build\Win64\UnrealEditor\Inc\SkeletalMeshModifiers\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\MeshModelingToolset\Source\SkeletalMeshModifiers\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\MeshModelingToolsetExp\Intermediate\Build\Win64\UnrealEditor\Inc\MeshModelingToolsEditorOnlyExp\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\MeshModelingToolsetExp\Intermediate\Build\Win64\UnrealEditor\Inc\MeshModelingToolsEditorOnlyExp\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\MeshModelingToolsetExp\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\MeshModelingToolsetExp\Source\MeshModelingToolsEditorOnlyExp\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\MeshModelingToolsetExp\Intermediate\Build\Win64\UnrealEditor\Inc\MeshModelingToolsExp\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\MeshModelingToolsetExp\Intermediate\Build\Win64\UnrealEditor\Inc\MeshModelingToolsExp\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\MeshModelingToolsetExp\Source\MeshModelingToolsExp\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\EditorDataStorageFeatures\Intermediate\Build\Win64\UnrealEditor\Inc\TedsOutliner\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\EditorDataStorageFeatures\Intermediate\Build\Win64\UnrealEditor\Inc\TedsOutliner\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\EditorDataStorageFeatures\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\EditorDataStorageFeatures\Source\TedsOutliner\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SharedSettingsWidgets\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\SharedSettingsWidgets\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SharedSettingsWidgets\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ExternalImagePicker\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\ExternalImagePicker\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ExternalImagePicker\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WorkspaceMenuStructure\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\WorkspaceMenuStructure\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorkspaceMenuStructure\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\XmlParser\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\XmlParser\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\XmlParser\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\KismetWidgets\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\KismetWidgets\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\KismetWidgets\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraph\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraph\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimGraph\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraphRuntime\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\AnimGraphRuntime\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AnimGraphRuntime\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\ChaosCaching\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosCaching\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\ChaosCaching\Intermediate\Build\Win64\UnrealEditor\Inc\ChaosCaching\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\ChaosCaching\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\ChaosCaching\Source\ChaosCaching\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelSequence\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\LevelSequence\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\LevelSequence\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StructUtilsEditor\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\StructUtilsEditor\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StructUtilsEditor\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessageLog\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\MessageLog\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MessageLog\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundEngine\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundEngine\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Source\MetasoundEngine\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundFrontend\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundFrontend\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Source\MetasoundFrontend\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Serialization\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\Serialization\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Serialization\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundGraphCore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundGraphCore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Source\MetasoundGraphCore\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundStandardNodes\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundStandardNodes\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Source\MetasoundStandardNodes\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\WaveTable\Intermediate\Build\Win64\UnrealEditor\Inc\WaveTable\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\WaveTable\Intermediate\Build\Win64\UnrealEditor\Inc\WaveTable\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\WaveTable\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\WaveTable\Source\WaveTable\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundGenerator\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Intermediate\Build\Win64\UnrealEditor\Inc\MetasoundGenerator\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\Metasound\Source\MetasoundGenerator\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ReplicationGraph\Intermediate\Build\Win64\UnrealEditor\Inc\ReplicationGraph\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ReplicationGraph\Intermediate\Build\Win64\UnrealEditor\Inc\ReplicationGraph\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ReplicationGraph\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ReplicationGraph\Source\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystem\Intermediate\Build\Win64\UnrealEditor\Inc\OnlineSubsystem\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystem\Intermediate\Build\Win64\UnrealEditor\Inc\OnlineSubsystem\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystem\Source\Test;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystem\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystem\Source\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineBase\Intermediate\Build\Win64\UnrealEditor\Inc\OnlineBase\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineBase\Intermediate\Build\Win64\UnrealEditor\Inc\OnlineBase\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineBase\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineBase\Source\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystemUtils\Intermediate\Build\Win64\UnrealEditor\Inc\OnlineSubsystemUtils\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystemUtils\Intermediate\Build\Win64\UnrealEditor\Inc\OnlineSubsystemUtils\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystemUtils\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystemUtils\Source\OnlineSubsystemUtils\Classes;C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystemUtils\Source\OnlineSubsystemUtils\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHICore\UHT;C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\UnrealEditor\Inc\RHICore\VNI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RHICore\Public;$(ProjectAdditionalIncludeDirectories_263);$(ProjectAdditionalIncludeDirectories_262);$(ProjectAdditionalIncludeDirectories_267);$(ProjectAdditionalIncludeDirectories_265);$(ProjectAdditionalIncludeDirectories_264);$(ProjectAdditionalIncludeDirectories_266);C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SymsLib;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SymsLib\syms</ClCompile_AdditionalIncludeDirectories>
    <ClCompile_AdditionalIncludeDirectories_1>$(NMakeIncludeSearchPath);$(ProjectAdditionalIncludeDirectories_86);..\Build\Win64\UnrealEditor\Inc\Auracron\UHT;..\Build\Win64\UnrealEditor\Inc\Auracron\VNI;..\..\Source;$(ProjectAdditionalIncludeDirectories_177);$(ProjectAdditionalIncludeDirectories_58);$(ProjectAdditionalIncludeDirectories_59);$(ProjectAdditionalIncludeDirectories_256);$(ProjectAdditionalIncludeDirectories_171);$(ProjectAdditionalIncludeDirectories_205);$(ProjectAdditionalIncludeDirectories_176);$(ProjectAdditionalIncludeDirectories_175);$(ProjectAdditionalIncludeDirectories_182);$(ProjectAdditionalIncludeDirectories_183);$(ProjectAdditionalIncludeDirectories_174);$(ProjectAdditionalIncludeDirectories_198);$(ProjectAdditionalIncludeDirectories_227);$(ProjectAdditionalIncludeDirectories_228);$(ProjectAdditionalIncludeDirectories_226);$(ProjectAdditionalIncludeDirectories_213);$(ProjectAdditionalIncludeDirectories_212);$(ProjectAdditionalIncludeDirectories_22);$(ProjectAdditionalIncludeDirectories_23);$(ProjectAdditionalIncludeDirectories_249);$(ProjectAdditionalIncludeDirectories_178);$(ProjectAdditionalIncludeDirectories_207);$(ProjectAdditionalIncludeDirectories_208);$(ProjectAdditionalIncludeDirectories_162);$(ProjectAdditionalIncludeDirectories);$(ProjectAdditionalIncludeDirectories_1);$(ProjectAdditionalIncludeDirectories_241);$(ProjectAdditionalIncludeDirectories_24);$(ProjectAdditionalIncludeDirectories_25);$(ProjectAdditionalIncludeDirectories_250);$(ProjectAdditionalIncludeDirectories_206);$(ProjectAdditionalIncludeDirectories_220);$(ProjectAdditionalIncludeDirectories_219);$(ProjectAdditionalIncludeDirectories_6);$(ProjectAdditionalIncludeDirectories_7);$(ProjectAdditionalIncludeDirectories_244);$(ProjectAdditionalIncludeDirectories_233);$(ProjectAdditionalIncludeDirectories_159);$(ProjectAdditionalIncludeDirectories_160);$(ProjectAdditionalIncludeDirectories_26);$(ProjectAdditionalIncludeDirectories_27);$(ProjectAdditionalIncludeDirectories_251);$(ProjectAdditionalIncludeDirectories_163);$(ProjectAdditionalIncludeDirectories_179);$(ProjectAdditionalIncludeDirectories_180);$(ProjectAdditionalIncludeDirectories_181);$(ProjectAdditionalIncludeDirectories_42);$(ProjectAdditionalIncludeDirectories_43);$(ProjectAdditionalIncludeDirectories_254);$(ProjectAdditionalIncludeDirectories_199);$(ProjectAdditionalIncludeDirectories_200);$(ProjectAdditionalIncludeDirectories_234);$(ProjectAdditionalIncludeDirectories_235);$(ProjectAdditionalIncludeDirectories_4);$(ProjectAdditionalIncludeDirectories_5);$(ProjectAdditionalIncludeDirectories_236);$(ProjectAdditionalIncludeDirectories_170);$(ProjectAdditionalIncludeDirectories_217);$(ProjectAdditionalIncludeDirectories_32);$(ProjectAdditionalIncludeDirectories_33);$(ProjectAdditionalIncludeDirectories_253);$(ProjectAdditionalIncludeDirectories_18);$(ProjectAdditionalIncludeDirectories_19);$(ProjectAdditionalIncludeDirectories_248);$(ProjectAdditionalIncludeDirectories_161);$(ProjectAdditionalIncludeDirectories_237);$(ProjectAdditionalIncludeDirectories_2);$(ProjectAdditionalIncludeDirectories_3);$(ProjectAdditionalIncludeDirectories_242);$(ProjectAdditionalIncludeDirectories_230);$(ProjectAdditionalIncludeDirectories_238);$(ProjectAdditionalIncludeDirectories_185);$(ProjectAdditionalIncludeDirectories_188);$(ProjectAdditionalIncludeDirectories_80);$(ProjectAdditionalIncludeDirectories_81);$(ProjectAdditionalIncludeDirectories_197);$(ProjectAdditionalIncludeDirectories_203);$(ProjectAdditionalIncludeDirectories_187);$(ProjectAdditionalIncludeDirectories_224);$(ProjectAdditionalIncludeDirectories_16);$(ProjectAdditionalIncludeDirectories_17);$(ProjectAdditionalIncludeDirectories_247);$(ProjectAdditionalIncludeDirectories_30);$(ProjectAdditionalIncludeDirectories_31);$(ProjectAdditionalIncludeDirectories_252);$(ProjectAdditionalIncludeDirectories_164);$(ProjectAdditionalIncludeDirectories_167);$(ProjectAdditionalIncludeDirectories_168);$(ProjectAdditionalIncludeDirectories_169);$(ProjectAdditionalIncludeDirectories_44);$(ProjectAdditionalIncludeDirectories_45);$(ProjectAdditionalIncludeDirectories_109);$(ProjectAdditionalIncludeDirectories_48);$(ProjectAdditionalIncludeDirectories_49);$(ProjectAdditionalIncludeDirectories_111);$(ProjectAdditionalIncludeDirectories_92);$(ProjectAdditionalIncludeDirectories_166);$(ProjectAdditionalIncludeDirectories_165);$(ProjectAdditionalIncludeDirectories_231);$(ProjectAdditionalIncludeDirectories_195);$(ProjectAdditionalIncludeDirectories_46);$(ProjectAdditionalIncludeDirectories_47);$(ProjectAdditionalIncludeDirectories_110);$(ProjectAdditionalIncludeDirectories_98);$(ProjectAdditionalIncludeDirectories_173);$(ProjectAdditionalIncludeDirectories_196);$(ProjectAdditionalIncludeDirectories_221);$(ProjectAdditionalIncludeDirectories_8);$(ProjectAdditionalIncludeDirectories_9);$(ProjectAdditionalIncludeDirectories_245);$(ProjectAdditionalIncludeDirectories_84);$(ProjectAdditionalIncludeDirectories_85);$(ProjectAdditionalIncludeDirectories_60);$(ProjectAdditionalIncludeDirectories_61);$(ProjectAdditionalIncludeDirectories_258);$(ProjectAdditionalIncludeDirectories_257);$(ProjectAdditionalIncludeDirectories_62);$(ProjectAdditionalIncludeDirectories_63);$(ProjectAdditionalIncludeDirectories_259);$(ProjectAdditionalIncludeDirectories_87);$(ProjectAdditionalIncludeDirectories_118);$(ProjectAdditionalIncludeDirectories_138);$(ProjectAdditionalIncludeDirectories_139);$(ProjectAdditionalIncludeDirectories_143);$(ProjectAdditionalIncludeDirectories_20);$(ProjectAdditionalIncludeDirectories_21);$(ProjectAdditionalIncludeDirectories_146);$(ProjectAdditionalIncludeDirectories_88);$(ProjectAdditionalIncludeDirectories_56);$(ProjectAdditionalIncludeDirectories_57);$(ProjectAdditionalIncludeDirectories_113);$(ProjectAdditionalIncludeDirectories_54);$(ProjectAdditionalIncludeDirectories_55);$(ProjectAdditionalIncludeDirectories_112);$(ProjectAdditionalIncludeDirectories_120);$(ProjectAdditionalIncludeDirectories_117);$(ProjectAdditionalIncludeDirectories_144);$(ProjectAdditionalIncludeDirectories_133);$(ProjectAdditionalIncludeDirectories_134);$(ProjectAdditionalIncludeDirectories_135);$(ProjectAdditionalIncludeDirectories_209);$(ProjectAdditionalIncludeDirectories_74);$(ProjectAdditionalIncludeDirectories_75);$(ProjectAdditionalIncludeDirectories_158);$(ProjectAdditionalIncludeDirectories_154);$(ProjectAdditionalIncludeDirectories_155);$(ProjectAdditionalIncludeDirectories_122);$(ProjectAdditionalIncludeDirectories_91);$(ProjectAdditionalIncludeDirectories_130);$(ProjectAdditionalIncludeDirectories_89);$(ProjectAdditionalIncludeDirectories_121);$(ProjectAdditionalIncludeDirectories_101);$(ProjectAdditionalIncludeDirectories_129);$(ProjectAdditionalIncludeDirectories_239);$(ProjectAdditionalIncludeDirectories_105);$(ProjectAdditionalIncludeDirectories_103);$(ProjectAdditionalIncludeDirectories_104);$(ProjectAdditionalIncludeDirectories_243);$(ProjectAdditionalIncludeDirectories_100);$(ProjectAdditionalIncludeDirectories_137);$(ProjectAdditionalIncludeDirectories_201);$(ProjectAdditionalIncludeDirectories_202);$(ProjectAdditionalIncludeDirectories_127);$(ProjectAdditionalIncludeDirectories_95);$(ProjectAdditionalIncludeDirectories_132);$(ProjectAdditionalIncludeDirectories_141);$(ProjectAdditionalIncludeDirectories_10);$(ProjectAdditionalIncludeDirectories_11);$(ProjectAdditionalIncludeDirectories_246);$(ProjectAdditionalIncludeDirectories_28);$(ProjectAdditionalIncludeDirectories_29);$(ProjectAdditionalIncludeDirectories_108);$(ProjectAdditionalIncludeDirectories_68);$(ProjectAdditionalIncludeDirectories_69);$(ProjectAdditionalIncludeDirectories_114);$(ProjectAdditionalIncludeDirectories_72);$(ProjectAdditionalIncludeDirectories_73);$(ProjectAdditionalIncludeDirectories_152);$(ProjectAdditionalIncludeDirectories_153);$(ProjectAdditionalIncludeDirectories_125);$(ProjectAdditionalIncludeDirectories_126);$(ProjectAdditionalIncludeDirectories_232);$(ProjectAdditionalIncludeDirectories_96);$(ProjectAdditionalIncludeDirectories_97);$(ProjectAdditionalIncludeDirectories_90);$(ProjectAdditionalIncludeDirectories_172);$(ProjectAdditionalIncludeDirectories_99);$(ProjectAdditionalIncludeDirectories_123);$(ProjectAdditionalIncludeDirectories_124);$(ProjectAdditionalIncludeDirectories_64);$(ProjectAdditionalIncludeDirectories_65);$(ProjectAdditionalIncludeDirectories_268);$(ProjectAdditionalIncludeDirectories_140);$(ProjectAdditionalIncludeDirectories_128);$(ProjectAdditionalIncludeDirectories_14);$(ProjectAdditionalIncludeDirectories_15);$(ProjectAdditionalIncludeDirectories_107);$(ProjectAdditionalIncludeDirectories_76);$(ProjectAdditionalIncludeDirectories_77);$(ProjectAdditionalIncludeDirectories_156);$(ProjectAdditionalIncludeDirectories_78);$(ProjectAdditionalIncludeDirectories_79);$(ProjectAdditionalIncludeDirectories_157);$(ProjectAdditionalIncludeDirectories_204);$(ProjectAdditionalIncludeDirectories_214);$(ProjectAdditionalIncludeDirectories_215);$(ProjectAdditionalIncludeDirectories_131);$(ProjectAdditionalIncludeDirectories_136);$(ProjectAdditionalIncludeDirectories_36);$(ProjectAdditionalIncludeDirectories_37);$(ProjectAdditionalIncludeDirectories_148);$(ProjectAdditionalIncludeDirectories_142);$(ProjectAdditionalIncludeDirectories_229);$(ProjectAdditionalIncludeDirectories_66);$(ProjectAdditionalIncludeDirectories_67);$(ProjectAdditionalIncludeDirectories_260);$(ProjectAdditionalIncludeDirectories_223);$(ProjectAdditionalIncludeDirectories_50);$(ProjectAdditionalIncludeDirectories_51);$(ProjectAdditionalIncludeDirectories_255);$(ProjectAdditionalIncludeDirectories_70);$(ProjectAdditionalIncludeDirectories_71);$(ProjectAdditionalIncludeDirectories_261);$(ProjectAdditionalIncludeDirectories_222);$(ProjectAdditionalIncludeDirectories_184);$(ProjectAdditionalIncludeDirectories_240);$(ProjectAdditionalIncludeDirectories_225);$(ProjectAdditionalIncludeDirectories_193);$(ProjectAdditionalIncludeDirectories_186);$(ProjectAdditionalIncludeDirectories_189);$(ProjectAdditionalIncludeDirectories_190);$(ProjectAdditionalIncludeDirectories_191);$(ProjectAdditionalIncludeDirectories_192);$(ProjectAdditionalIncludeDirectories_194);$(ProjectAdditionalIncludeDirectories_102);$(ProjectAdditionalIncludeDirectories_218);$(ProjectAdditionalIncludeDirectories_216);$(ProjectAdditionalIncludeDirectories_52);$(ProjectAdditionalIncludeDirectories_53);$(ProjectAdditionalIncludeDirectories_151);$(ProjectAdditionalIncludeDirectories_34);$(ProjectAdditionalIncludeDirectories_35);$(ProjectAdditionalIncludeDirectories_147);$(ProjectAdditionalIncludeDirectories_210);$(ProjectAdditionalIncludeDirectories_211);$(ProjectAdditionalIncludeDirectories_93);$(ProjectAdditionalIncludeDirectories_94);$(ProjectAdditionalIncludeDirectories_38);$(ProjectAdditionalIncludeDirectories_39);$(ProjectAdditionalIncludeDirectories_149);$(ProjectAdditionalIncludeDirectories_40);$(ProjectAdditionalIncludeDirectories_41);$(ProjectAdditionalIncludeDirectories_150);$(ProjectAdditionalIncludeDirectories_106);$(ProjectAdditionalIncludeDirectories_82);$(ProjectAdditionalIncludeDirectories_83);$(ProjectAdditionalIncludeDirectories_115);$(ProjectAdditionalIncludeDirectories_116);$(ProjectAdditionalIncludeDirectories_12);$(ProjectAdditionalIncludeDirectories_13);$(ProjectAdditionalIncludeDirectories_145);$(ProjectAdditionalIncludeDirectories_119);$(ProjectAdditionalIncludeDirectories_263);$(ProjectAdditionalIncludeDirectories_262);$(ProjectAdditionalIncludeDirectories_267);$(ProjectAdditionalIncludeDirectories_265);$(ProjectAdditionalIncludeDirectories_264);$(ProjectAdditionalIncludeDirectories_266)</ClCompile_AdditionalIncludeDirectories_1>
    <ProjectForcedIncludeFiles>$(SolutionDir)Intermediate\Build\Win64\x64\AuracronEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h</ProjectForcedIncludeFiles>
    <ClCompile_ForcedIncludeFiles>$(ProjectForcedIncludeFiles);$(SolutionDir)Plugins\UnrealMCP\Intermediate\Build\Win64\x64\UnrealEditor\Development\UnrealMCP\Definitions.UnrealMCP.h</ClCompile_ForcedIncludeFiles>
    <ClCompile_ForcedIncludeFiles_1>$(ProjectForcedIncludeFiles);$(SolutionDir)Intermediate\Build\Win64\x64\UnrealEditor\Development\Auracron\Definitions.Auracron.h</ClCompile_ForcedIncludeFiles_1>
  </PropertyGroup>
  <ItemGroup>
    <None Include="..\..\Auracron.uproject"/>
    <None Include="..\..\Source\Auracron.Target.cs"/>
    <None Include="..\..\Source\AuracronEditor.Target.cs"/>
    <None Include="..\..\.vsconfig"/>
    <None Include="..\..\unreal_mcp.log"/>
    <None Include="..\..\Config\DefaultEditor.ini"/>
    <None Include="..\..\Config\DefaultEngine.ini"/>
    <None Include="..\..\Config\DefaultGame.ini"/>
    <None Include="..\..\Config\DefaultInput.ini"/>
    <None Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\UnrealMCP.Build.cs"/>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\MCPServerRunnable.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPBridge.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPModule.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPAICommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPAnalyticsTelemetryCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPAudioSystemCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBackendServicesCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBlueprintCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBlueprintNodeCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPChaosPhysicsCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCloudServicesCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCollisionCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCombatMechanicsCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCommonUtils.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPEditorCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPGamePhasesCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPHardwareDetectionCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPJungleSystemCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPLaneMechanicsCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMaterialCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMultilayerMapCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPNetworkCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPNetworkingCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPNiagaraCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPObjectivesStructuresCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPPathfindingCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPPerformanceCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPPlatformCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPProceduralCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPProjectCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPRealmCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPRenderingPipelineCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPSecurityAntiCheatCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPUMGCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPVerticalNavigationCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPVisionCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPWorldPartitionCommands.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\MCPServerRunnable.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\UnrealMCPBridge.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\UnrealMCPModule.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPAICommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPAnalyticsTelemetryCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPAudioSystemCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPBackendServicesCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPBlueprintCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPBlueprintNodeCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPChaosPhysicsCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCloudServicesCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCollisionCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCombatMechanicsCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCommonUtils.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPEditorCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPGamePhasesCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPHardwareDetectionCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPJungleSystemCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPLaneMechanicsCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPMaterialCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPMultilayerMapCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPNetworkCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPNetworkingCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPNiagaraCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPObjectivesStructuresCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPPathfindingCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPPerformanceCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPPlatformCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPProceduralCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPProjectCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPRealmCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPRenderingPipelineCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPSecurityAntiCheatCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPUMGCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPVerticalNavigationCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPVisionCommands.h"/>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPWorldPartitionCommands.h"/>
    <None Include="..\..\Plugins\UnrealMCP\UnrealMCP.uplugin"/>
    <None Include="..\..\Source\Auracron\Auracron.Build.cs"/>
    <ClCompile Include="..\..\Source\Auracron\Auracron.cpp">
      <AdditionalIncludeDirectories>$(ClCompile_AdditionalIncludeDirectories_1)</AdditionalIncludeDirectories>
      <ForcedIncludeFiles>$(ClCompile_ForcedIncludeFiles_1)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="..\..\Source\Auracron\Auracron.h"/>
  </ItemGroup>
  <PropertyGroup>
    <SourcePath>C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TreeMap;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UATHelper;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libJPG;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AITestSuite\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AnimationDataController\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AnimationWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AssetTools\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AudioFormatADPCM\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AudioFormatBink\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AudioFormatOgg\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AudioFormatOpus\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AudioFormatRad\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AudioSettingsEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AutomationController\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AutomationDriver\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AutomationWindow\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\BlankModule\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\BSPUtils\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CollectionManager\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CollisionAnalyzer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CookedEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CookMetadata\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CookOnTheFlyNetServer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CQTest\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CrashDebugHelper\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CSVUtils\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DerivedDataCache\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DerivedDataCache\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DesktopPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DesktopWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeveloperToolSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DevHttp\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeviceManager\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DirectoryWatcher\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DrawPrimitiveDebugger\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\EditorAnalyticsSession\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ExternalImagePicker\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\FileUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\FunctionalTesting\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\GeometryProcessingInterfaces\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\GraphColor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\HierarchicalLODUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Horde\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\HotReload\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\IoStoreUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LauncherServices\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Localization\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LocalizationService\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LogVisualizer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MassEntityTestSuite\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MaterialBaking\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MaterialUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Merge\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshBoneReduction\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshBuilder\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshBuilderCommon\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshDescriptionOperations\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshMergeUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshReductionInterface\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshSimplifier\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MeshUtilitiesEngine\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MessageLog\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\NaniteBuilder\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\NaniteUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\OutputLog\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\PakFileUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\PhysicsUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Profiler\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ProfilerClient\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ProfilerMessages\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ProfilerService\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ProfileVisualizer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\S3Client\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ScreenShotComparison\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ScreenShotComparisonTools\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ScriptDisassembler\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SessionFrontend\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Settings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SettingsEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ShaderCompilerCommon\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ShaderFormatOpenGL\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ShaderFormatVectorVM\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ShaderPreprocessor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ShaderPreprocessor\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SharedSettingsWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SkeletalMeshUtilitiesCommon\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SlackIntegrations\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SlateFileDialogs\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SlateFontDialog\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SlateReflector\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SourceCodeAccess\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SourceControl\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SourceControlCheckInPrompt\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SourceControlViewport\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\StandaloneRenderer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\StructUtilsTestSuite\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TargetDeviceServices\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TargetPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureBuild\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureBuildUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureCompressor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureFormat\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureFormatASTC\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureFormatDXT\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureFormatETC2\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureFormatIntelISPCTexComp\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TextureFormatUncompressed\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ToolMenus\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ToolWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceAnalysis\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceServices\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceTools\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TranslationEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TurnkeyIO\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\UbaCoordinatorHorde\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\UncontrolledChangelists\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\UndoHistory\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Virtualization\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\VisualGraphUtils\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\VulkanShaderFormat\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Zen\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Zen\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ZenPluggableTransport\winsock;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ActionableMessage\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ActorPickerMode\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AddContentDialog\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AdvancedPreviewScene\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AIGraph\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationBlueprintEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationBlueprintLibrary\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationEditMode\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationEditorWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationModifiers\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimGraph\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AssetDefinition\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AssetTagsEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AudioEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\BehaviorTreeEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\BlueprintEditorLibrary\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\BlueprintGraph\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Blutility\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ClassViewer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ClothingSystemEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ClothingSystemEditorInterface\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ClothPainter\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CommonMenuExtensions\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ComponentVisualizers\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ConfigEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ContentBrowser\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ContentBrowserData\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CSVtoSVG\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveAssetEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveTableEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DataHierarchyEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DataLayerEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DataTableEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DerivedDataEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DerivedDataWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DetailCustomizations\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DeviceProfileEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DeviceProfileServices\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DistCurveEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Documentation\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorConfig\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorFramework\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorSettingsViewer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorStyle\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorSubsystem\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EnvironmentLightingViewer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\FoliageEdit\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\FontEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\GameplayDebugger\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\GameplayTasksEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\GameProjectGeneration\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\GraphEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\HardwareTargeting\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\HierarchicalLODOutliner\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\InputBindingEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\InternationalizationSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Kismet\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\KismetCompiler\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\KismetWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LandscapeEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LandscapeEditorUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Layers\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LevelEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LevelInstanceEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LocalizationCommandletExecution\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LocalizationDashboard\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MainFrame\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MassEntityDebugger\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MassEntityEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MaterialEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MergeActors\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MeshPaint\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneCaptureDialog\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\NewLevelDialog\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\NNEEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\OverlayEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PackagesDialog\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Persona\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Persona\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PhysicsAssetEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PIEPreviewDeviceProfileSelector\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PIEPreviewDeviceSpecification\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PinnedCommandList\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PixelInspector\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PlacementMode\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PListEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PluginWarden\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ProjectSettingsViewer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ProjectTargetPlatformEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\RenderResourceViewer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\RewindDebuggerInterface\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SceneDepthPickerMode\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SceneOutliner\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ScriptableEditorWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequenceRecorder\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequenceRecorderSections\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SerializedRecorderInterface\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SkeletalMeshEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SkeletonEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SourceControlWindowExtender\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SourceControlWindows\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SparseVolumeTexture\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StaticMeshEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StatsViewer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StatusBar\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StorageServerWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StringTableEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StructUtilsEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StructViewer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SubobjectDataInterface\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SubobjectEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SVGDistanceField\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SwarmInterface\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\TextureEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ToolMenusEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\TurnkeySupport\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Classes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UndoHistoryEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UniversalObjectLocatorEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEdMessages\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ViewportInteraction\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ViewportSnapping\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\VirtualizationEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\VirtualTexturingEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\VREditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorkspaceMenuStructure\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldBrowser\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldPartitionEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ZenEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Programs\Shared\EpicGames.Perforce.Native;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AdvancedWidgets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AnimationCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AnimGraphRuntime\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AppFramework\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AssetRegistry\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AssetRegistry\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioAnalyzer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioCaptureCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioExtensions\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixerCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioPlatformConfiguration\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AugmentedReality\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AutomationMessages\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AutomationTest\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AutomationWorker\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AutoRTFM\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AVEncoder\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AVIWriter\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\BlueprintRuntime\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\BuildSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Cbor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Cbor\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CEF3Utils\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CinematicCamera\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ClientPilot\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ClothingSystemRuntimeCommon\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ClothingSystemRuntimeInterface\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ClothingSystemRuntimeNv\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ColorManagement\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CookOnTheFly\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CrashReportCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\D3D12RHI\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\DeveloperSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\EngineMessages\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\EngineSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ExternalRPCRegistry\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\EyeTracker\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\FieldNotification\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Foliage\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\FriendsAndChat\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameMenuBuilder\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayDebugger\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayMediaEncoder\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayTags\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayTasks\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryFramework\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\HardwareSurvey\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\HeadMountedDisplay\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\IESFile\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ImageCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ImageWrapper\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ImageWriteQueue\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputDevice\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InstallBundleManager\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\IPC\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Json\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\JsonUtilities\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Landscape\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Launch\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\LevelSequence\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\LiveLinkAnimationCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\LiveLinkInterface\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\LiveLinkMessageBusFramework\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MassEntity\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MaterialShaderQualitySettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Media\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MediaAssets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MediaUtils\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MeshConversion\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MeshConversionEngineTypes\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MeshDescription\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MeshUtilitiesCommon\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Messaging\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MessagingCommon\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MessagingRpc\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MoviePlayer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MoviePlayerProxy\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneCapture\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MRMesh\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MRMesh\Public;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NavigationSystem\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Navmesh\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkFile\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkFileSystem\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Networking\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NNE\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NonRealtimeAudioRenderer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NullDrv\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NullInstallBundleManager\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\OpenColorIOWrapper\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\OpenGLDrv\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Overlay\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PakFile\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PerfCounters\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PhysicsCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PreLoadScreen\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Projects\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PropertyPath\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RawMesh\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RenderCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RewindDebuggerRuntimeInterface\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RHI\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RHICore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RSA\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RuntimeAssetCache\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SandboxFile\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Serialization\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SessionMessages\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SessionServices\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SignalProcessing\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SkeletalMeshDescription\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateNullRenderer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateRHIRenderer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Sockets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SoundFieldRendering\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StateStream\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StaticMeshDescription\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StorageServerClient\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StorageServerClientDebug\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StreamingFile\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StreamingPauseRendering\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StudioTelemetry\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SynthBenchmark\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TextureUtilitiesCommon\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TimeManagement\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Toolbox\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementRuntime\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UELibrary\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UniversalObjectLocator\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UnrealGame\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VectorVM\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VirtualFileCache\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VulkanRHI\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\WebBrowser\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\WebBrowserTexture\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\WidgetCarousel\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\XmlParser\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Android\detex;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libGPUCounters\Source;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libSampleRate\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\nanosvg\src;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AITestSuite\Private\BehaviorTree;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AITestSuite\Private\MockAI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AITestSuite\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Android\AndroidDeviceDetection\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Android\AndroidPlatformEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Android\AndroidTargetPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Android\AndroidTargetPlatformControls\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Android\AndroidTargetPlatformSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Android\AndroidZenServerPlugin\ZenServerAdapter;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Apple\MetalShaderFormat\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AssetTools\Private\AssetTypeActions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\AutomationDriver\Private\Locators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CQTest\Private\Commands;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CQTest\Private\Components;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CQTest\Private\Helpers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CrashDebugHelper\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CrashDebugHelper\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CrashDebugHelper\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CrashDebugHelper\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\CrashDebugHelper\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Datasmith\DatasmithExporter\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Datasmith\DatasmithExporterUI\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Datasmith\DatasmithFacade\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DerivedDataCache\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DesktopPlatform\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DesktopPlatform\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DesktopPlatform\Private\Null;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DesktopPlatform\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeviceManager\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DirectoryWatcher\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DirectoryWatcher\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DirectoryWatcher\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DirectoryWatcher\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\FileUtilities\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\FunctionalTesting\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Horde\Private\Compute;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Horde\Private\Server;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Horde\Private\Storage;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\IOS\IOSPlatformEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\IOS\IOSTargetPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\IOS\IOSTargetPlatformControls\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\IOS\IOSTargetPlatformSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\IOS\TVOSTargetPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\IOS\TVOSTargetPlatformControls\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\IOS\TVOSTargetPlatformSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LauncherServices\Private\Launcher;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LauncherServices\Private\Profiles;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Linux\LinuxArm64TargetPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Linux\LinuxArm64TargetPlatformControls\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Linux\LinuxArm64TargetPlatformSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Linux\LinuxPlatformEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Linux\LinuxTargetPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Linux\LinuxTargetPlatformControls\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Linux\LinuxTargetPlatformSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Localization\Private\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private\TestCommon;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private\TestListeners;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private\TestStubs;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Mac\MacPlatformEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Mac\MacTargetPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Mac\MacTargetPlatformControls\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Mac\MacTargetPlatformSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MessageLog\Private\Model;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MessageLog\Private\Presentation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\MessageLog\Private\UserInterface;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\PakFileUtilities\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Profiler\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ScreenShotComparison\Private\Models;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ScreenShotComparison\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SessionFrontend\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SettingsEditor\Private\Customizations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SettingsEditor\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ShaderCompilerCommon\Private\ISAParser;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SlateReflector\Private\Models;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SlateReflector\Private\Styling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SlateReflector\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SourceControl\Private\RevisionControlStyle;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\StandaloneRenderer\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\StandaloneRenderer\Private\OpenGL;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TargetDeviceServices\Private\Proxies;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TargetDeviceServices\Private\Services;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ToolMenus\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ToolWidgets\Private\Dialog;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ToolWidgets\Private\Filters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\ToolWidgets\Private\Sidebar;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceAnalysis\Private\Analysis;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceAnalysis\Private\Asio;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceAnalysis\Private\Store;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceServices\Private\Analyzers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceServices\Private\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceServices\Private\Model;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceServices\Private\Modules;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceServices\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceTools\Private\Models;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceTools\Private\Services;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceTools\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\UndoHistory\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\UnsavedAssetsTracker\Source\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Private\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Private\DataVisualization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Private\Inputs;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Private\Layout;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Private\Persistence;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Private\Styles;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Public\Inputs;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Windows\LiveCoding\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Windows\LiveCodingServer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Windows\ShaderFormatD3D\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Windows\WindowsPlatformEditor\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Windows\WindowsTargetPlatfomControls\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Windows\WindowsTargetPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Windows\WindowsTargetPlatformSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AddContentDialog\Private\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationBlueprintEditor\Private\AnimationNodes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationBlueprintEditor\Private\AnimationPins;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationBlueprintEditor\Private\AnimationStateNodes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationEditorWidgets\Private\Overrides;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimationEditorWidgets\Private\SchematicGraphPanel;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AnimGraph\Private\EditModes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AudioEditor\Private\AssetTypeActions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AudioEditor\Private\Editors;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AudioEditor\Private\Factories;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\BehaviorTreeEditor\Private\DetailCustomizations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\BlueprintGraph\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ConfigEditor\Private\PropertyVisualization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ContentBrowser\Private\AssetView;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ContentBrowser\Private\Experimental;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ContentBrowser\Private\Menus;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ContentBrowser\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private\DragOperations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private\Filters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private\Modification;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private\Tree;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private\Views;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DataHierarchyEditor\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DataLayerEditor\Private\DataLayer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\DeviceProfileEditor\Private\DetailsPanel;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorConfig\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorFramework\Private\Factories;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorFramework\Private\Subsystems;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorFramework\Private\Toolkits;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorFramework\Private\Tools;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorFramework\Private\Viewports;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorWidgets\Private\Filters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\GameProjectGeneration\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\GraphEditor\Private\KismetNodes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\GraphEditor\Private\KismetPins;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\InputBindingEditor\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Kismet\Private\Blueprints;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Kismet\Private\Debugging;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Kismet\Private\ProjectUtilities;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Kismet\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LandscapeEditor\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LevelEditor\Private\ViewportToolbar;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MainFrame\Private\Frame;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MainFrame\Private\Menus;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MaterialEditor\Private\MaterialNodes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MaterialEditor\Private\MaterialPins;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MaterialEditor\Private\Tabs;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MaterialEditor\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MergeActors\Private\MergeProxyUtils;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MergeActors\Private\MeshApproximationTool;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MergeActors\Private\MeshInstancingTool;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MergeActors\Private\MeshMergingTool;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MergeActors\Private\MeshProxyTool;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\Bindings;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\Cache;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\Channels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\Conditions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\Constraints;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\CurveKeyEditors;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\EditModes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\FCPXML;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\MVVM;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\Sections;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\TrackEditors;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\TrackEditorThumbnail;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\OverlayEditor\Private\Factories;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Persona\Private\AnimTimeline;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Persona\Private\Customization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Persona\Private\Shared;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Persona\Private\ViewportToolbar;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PhysicsAssetEditor\Private\PhysicsAssetGraph;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ProjectTargetPlatformEditor\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ScriptableEditorWidgets\Private\Components;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Capabilities;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Filters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Menus;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\MVVM;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Scripting;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Tools;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Private\MVVM;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Private\Scripting;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequenceRecorder\Private\Sections;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StatsViewer\Private\StatsEntries;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StatsViewer\Private\StatsPages;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\StructUtilsEditor\Private\Customizations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\TextureEditor\Private\Customizations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\TextureEditor\Private\Models;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\TextureEditor\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Animation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\BlueprintModes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Components;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Customizations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Designer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Details;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\DragDrop;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Extensions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\FieldNotification;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Graph;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Hierarchy;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Library;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Navigation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Nodes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Palette;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Preview;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Settings;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\TabFactory;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Templates;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\ToolPalette;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Utility;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UMGEditor\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UndoHistoryEditor\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Analytics;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Animation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\AutoReimport;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Bookmarks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Commandlets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Cooker;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Dialogs;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\DragAndDrop;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Editor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\EditorDomain;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\EditorState;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Factories;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Fbx;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Features;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\ImportUtils;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Instances;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Kismet2;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Layers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Lightmass;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\MaterialEditor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Settings;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\StaticLightingSystem;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Subsystems;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\TargetDomain;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Text;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\ThumbnailRendering;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Toolkits;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Tools;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\ViewportToolbar;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\WorkflowOrientedApp;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\WorldPartition;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Public\Elements;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ViewportInteraction\Private\Gizmo;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\VREditor\Private\Teleporter;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\VREditor\Private\UI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldBookmark\Private\WorldBookmark;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldBrowser\Private\StreamingLevels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldBrowser\Private\Tiles;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AdpcmAudioDecoder\Module\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AdvancedWidgets\Private\Components;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AdvancedWidgets\Private\Slate;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AdvancedWidgets\Private\Styling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AdvancedWidgets\Private\Util;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Advertising\Advertising\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\BehaviorTree;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\Blueprint;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\DataProviders;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\GameplayDebugger;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\HotSpots;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\Navigation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\Perception;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\Tasks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\Analytics\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\AnalyticsET\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\AnalyticsHorde\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\AnalyticsLog\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\AnalyticsSwrve\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\AnalyticsVisualEditing\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\TelemetryUtils\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\TelemetryUtils\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Android\AndroidLocalNotification\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Android\AndroidRuntimeSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Android\AudioMixerAndroid\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AnimationCore\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AnimGraphRuntime\Private\AnimNodes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AnimGraphRuntime\Private\BoneControllers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AnimGraphRuntime\Private\RBF;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Apple\AudioMixerAudioUnit\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Apple\AudioMixerCoreAudio\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Apple\MetalRHI\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\Apple;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\GenericPlatform;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\HAL;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\Null;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\Unix;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioCaptureImplementations\AudioCaptureRtAudio\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioLink\AudioLinkEngine\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioLink\AudioMixerPlatformAudioLink\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixer\Private\Components;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixer\Private\Effects;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixer\Private\Generators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixer\Private\Quartz;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioMixer\Private\SoundFileIO;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AVEncoder\Private\Decoders;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AVEncoder\Private\Encoders;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\BinkAudioDecoder\Module\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CEF3Utils\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ClothingSystemRuntimeCommon\Private\Utils;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Internal\IO;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Apple;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Async;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Audio;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\AutoRTFM;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\ColorManagement;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Compression;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Containers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Delegates;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Experimental;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Features;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\FileCache;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\FramePro;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\GenericPlatform;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\HAL;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Hash;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Instrumentation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Internationalization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\IO;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Logging;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Math;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Memory;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\MemPro;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Microsoft;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Modules;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\ProfilingDebugging;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Sanitizer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Stats;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\String;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Tasks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Templates;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Traits;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Unix;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\UObject;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Virtualization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Algo;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Async;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Compression;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Containers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Delegates;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\GenericPlatform;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\HAL;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Hash;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Internationalization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\IO;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Logging;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Math;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Memory;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Sanitizer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\String;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Tasks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Tests\Templates;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreOnline\Private\Online;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreOnline\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CorePreciseFP\Private\Math;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CorePreciseFP\Private\VerseVM;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\AssetRegistry;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\Blueprint;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\Cooker;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\Internationalization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\StructUtils;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\Templates;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\UObject;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\VerseVM;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Public\VerseVM;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Tests\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Tests\UObject;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CrashReportCore\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CrashReportCore\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CrashReportCore\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CrashReportCore\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CUDA\Source\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\D3D12RHI\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\DatasmithCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\DirectLink\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\DeveloperSettings\Private\Engine;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Classes\Animation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Classes\Engine;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Classes\Sound;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Internal\Materials;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Internal\ShaderCompiler;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Internal\Streaming;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\ActorEditorContext;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\ActorPartition;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\AI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Analytics;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Animation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Atmosphere;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Audio;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Camera;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Collision;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Commandlets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Components;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Curves;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\DataDrivenCVars;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Debug;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\DeviceProfiles;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\EdGraph;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\EditorFramework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Engine;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Experimental;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\FieldNotification;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\GameFramework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\HLOD;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\InstanceData;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\InstancedStaticMesh;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Instances;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Internationalization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\ISMPartition;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Kismet;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Layers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\LevelInstance;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\MaterialCache;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Materials;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\MeshMerge;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\MeshVertexPainter;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Net;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\ODSC;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\PackedLevelActor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\PacketHandlers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Particles;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Performance;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\PhysicsEngine;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\PhysicsField;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\ProfilingDebugging;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Rendering;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Shader;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\ShaderCompiler;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Slate;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\SparseVolumeTexture;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Streaming;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Subsystems;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Subtitles;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\UniversalObjectLocators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\UserInterface;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Vehicles;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\VisualLogger;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\VT;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Public\Rendering;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosVDData\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosVisualDebugger\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\JsonObjectGraph\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\FieldNotification\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayTags\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GameplayTasks\Private\Tasks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Clustering;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\CompGeom;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\DynamicMesh;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Generators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Implicit;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Intersection;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Operations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Parameterization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Sampling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Selections;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Spatial;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\Util;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryFramework\Private\Changes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryFramework\Private\Components;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ImageWrapper\Private\Formats;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputCore\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputCore\Private\GenericPlatform;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputCore\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputCore\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputCore\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InputCore\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Instrumentation\Private\Instrumentation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Private\BaseBehaviors;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Private\BaseGizmos;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Private\BaseTools;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Private\Changes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Private\SceneQueries;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Private\ToolTargets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Interchange\Core\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Interchange\Engine\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\IOS\IOSAudio\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\IOS\IOSLocalNotification\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\IOS\IOSPlatformFeatures\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\IOS\IOSRuntimeSettings\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\IOS\LaunchDaemonMessages\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\IOS\MarketplaceKit\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Json\Private\Dom;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Json\Private\JsonUtils;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Json\Private\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Json\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Landscape\Private\Materials;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Launch\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Launch\Private\Apple;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Launch\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Launch\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Launch\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Launch\Private\Unix;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Launch\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Linux\AudioMixerSDL\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\LiveLinkInterface\Private\Roles;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MathCore\Private\Graph;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MediaAssets\Private\Assets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MediaAssets\Private\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MediaAssets\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MeshDescription\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Messaging\Private\Bridge;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Messaging\Private\Bus;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Bindings;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Channels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Compilation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Conditions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Decorations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\EntitySystem;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Evaluation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\EventHandlers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Generators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\MetaData;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Sections;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Tracks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Variants;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\Bindings;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\Channels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\Conditions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\Evaluation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\PreAnimatedState;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\Sections;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\Systems;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\TrackInstances;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\Tracks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NavigationSystem\Private\NavAreas;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NavigationSystem\Private\NavFilters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NavigationSystem\Private\NavGraph;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NavigationSystem\Private\NavMesh;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NavigationSystem\Public\NavMesh;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Navmesh\Private\DebugUtils;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Navmesh\Private\Detour;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Navmesh\Private\DetourCrowd;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Navmesh\Private\DetourTileCache;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Navmesh\Private\Recast;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Networking\Private\IPv4;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Networking\Private\Steam;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Networking\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkReplayStreaming\HttpNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkReplayStreaming\InMemoryNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkReplayStreaming\LocalFileNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkReplayStreaming\NetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkReplayStreaming\NullNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\NetworkReplayStreaming\SaveGameNetworkReplayStreaming\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BackgroundHTTP\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BackgroundHTTPFileHash\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTPServer\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\ICMP\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\ImageDownload\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\SSL\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Stomp\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Voice\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\WebSockets\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\XMPP\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\OpenColorIOWrapper\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\OpenGLDrv\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\OpenGLDrv\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\OpenGLDrv\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\OpusAudioDecoder\Module\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Overlay\Private\Assets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Overlay\Private\Factories;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PacketHandlers\PacketHandler\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PacketHandlers\ReliabilityHandlerComponent\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PlatformThirdPartyHelpers\PosixShim\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\LauncherCheck\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\LauncherPlatform\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\Messages\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\Proxies\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\Rpc\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\Services\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\PropertyPath\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RadAudioCodec\Module\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RenderCore\Private\Animation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RenderCore\Private\ProfilingDebugging;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\CompositionLighting;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\Froxel;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\HairStrands;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\HeterogeneousVolumes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\InstanceCulling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\Lumen;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\MaterialCache;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\MegaLights;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\Nanite;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\OIT;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\PostProcess;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\RayTracing;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\Renderer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\SceneCulling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\Shadows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\Skinning;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\SparseVolumeTexture;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\StateStream;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\StochasticLighting;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\Substrate;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\VariableRateShading;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\VirtualShadowMaps;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\VT;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RHI\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RHI\Private\Apple;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RHI\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RHI\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Serialization\Private\Backends;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Serialization\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Animation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Application;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Brushes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Debugging;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\FastUpdate;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Fonts;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Input;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Layout;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Rendering;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Sound;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Styling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Test;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Textures;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Trace;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Types;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateRHIRenderer\Private\FX;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Sockets\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Sockets\Private\BSDSockets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Sockets\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Sockets\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Sockets\Private\Unix;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Sockets\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Solaris\uLangUE\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StorageServerClient\Private\BuiltInHttpClient;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\StorageServerClient\Private\Cache;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TimeManagement\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TraceLog\Private\Trace;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Private\Animation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Private\Binding;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Private\Blueprint;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Private\Components;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Private\Extensions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Private\Slate;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Unix\UnixCommonStartup\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VerseCompiler\Private\uLang;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VirtualProduction\StageDataCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VirtualProduction\VirtualProduction\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VorbisAudioDecoder\Module\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VulkanRHI\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VulkanRHI\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VulkanRHI\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\WebBrowser\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\WebBrowser\Private\CEF;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\WebBrowser\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\WebBrowser\Private\MobileJS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\WebBrowser\Private\Native;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Windows\AudioMixerWasapi\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Windows\AudioMixerXAudio2\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Windows\D3D11RHI\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Windows\WindowsPlatformFeatures\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\extras;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Eigen\unsupported\bench;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Eigen\unsupported\test;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libGPUCounters\Source\src;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Datasmith\DatasmithExporterUI\Private\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Datasmith\DatasmithFacade\Private\DirectLink;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DesktopWidgets\Private\Widgets\Input;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeviceManager\Private\Widgets\Apps;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeviceManager\Private\Widgets\Browser;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeviceManager\Private\Widgets\Details;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeviceManager\Private\Widgets\Processes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\DeviceManager\Private\Widgets\Toolbar;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Horde\Private\Storage\Bundles;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Horde\Private\Storage\Clients;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Horde\Private\Storage\Nodes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Archive;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Build;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Cook;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Deploy;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Launch;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Package;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Preview;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Profile;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Progress;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Project;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Settings;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LegacyProjectLauncher\Private\Widgets\Shared;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Apple;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\LowLevelTestsRunner\Private\Platform\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SessionFrontend\Private\Widgets\Browser;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\SessionFrontend\Private\Widgets\Console;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\StandaloneRenderer\Private\Linux\OpenGL;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\StandaloneRenderer\Private\Mac\OpenGL;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\StandaloneRenderer\Private\Windows\D3D;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\StandaloneRenderer\Private\Windows\OpenGL;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceAnalysis\Private\Analysis\Transport;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\ContextSwitches;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\CookProfiler;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\ImportTool;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\NetworkingProfiler;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\TaskGraphProfiler;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\TimingProfiler;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\StoreService;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\WidgetRegistration\Private\Layout\Containers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Windows\LiveCoding\Private\External;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Windows\LiveCodingServer\Private\External;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\AddContentDialog\Private\ContentSourceProviders\FeaturePack;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\ContentBrowser\Private\ContentSources\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private\Misc\Mirror;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private\Modification\Changes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\CurveEditor\Private\Modification\Utils;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\EditorFramework\Private\Elements\Framework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private\Behaviors;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private\EditorDragTools;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private\EditorGizmos;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private\Snapping;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Experimental\EditorInteractiveToolsFramework\Private\ToolContexts;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LevelEditor\Private\Elements\Actor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LevelEditor\Private\Elements\Component;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\LevelEditor\Private\Elements\SMInstance;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\MVVM\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\MovieSceneTools\Private\TrackEditors\PropertyTrackEditors;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private\Presentation\PropertyEditor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private\Presentation\PropertyTable;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private\UserInterface\Categories;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private\UserInterface\PropertyDetails;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private\UserInterface\PropertyEditor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private\UserInterface\PropertyTable;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private\UserInterface\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Filters\Filters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Filters\Menus;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Filters\TextExpressions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Filters\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Misc\Thumbnail;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\MVVM\Extensions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\MVVM\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\MVVM\Views;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Widgets\CurveEditor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Widgets\OutlinerColumns;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Widgets\OutlinerIndicators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\Widgets\Sidebar;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Private\MVVM\Extensions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Private\MVVM\Selection;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Private\MVVM\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Private\MVVM\Views;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Cooker\Algo;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Elements\Actor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Elements\Component;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Elements\Framework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Elements\Object;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\UnrealEd\Private\Elements\SMInstance;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldBookmark\Private\WorldBookmark\Browser;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\ContentBundle;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\Customizations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\Filter;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\HLOD;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Programs\LiveLinkHub\Source\LiveLinkHubLauncher\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AdvancedWidgets\Private\Framework\PropertyViewer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AdvancedWidgets\Private\Widgets\ColorGrading;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AdvancedWidgets\Private\Widgets\PropertyViewer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Advertising\Android\AndroidAdvertising\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Advertising\IOS\IOSAdvertising\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Blackboard;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Composites;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Decorators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Services;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\BehaviorTree\Tasks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Contexts;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Generators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Items;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AIModule\Private\EnvironmentQuery\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\Analytics\Private\Interfaces;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\Analytics\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Analytics\AnalyticsET\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AppFramework\Private\Framework\Testing;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AppFramework\Private\Widgets\Colors;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AppFramework\Private\Widgets\Testing;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AppFramework\Private\Widgets\Workflow;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Apple\MetalRHI\Private\Shaders;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\GenericPlatform\Accessibility;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\IOS\Accessibility;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\Mac\Accessibility;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\ApplicationCore\Private\Windows\Accessibility;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioCaptureImplementations\Android\AudioCaptureAndroid\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioCaptureImplementations\IOS\AudioCaptureAudioUnit\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioCaptureImplementations\Windows\AudioCaputureWasapi\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioDeviceEnumeration\Windows\WindowsMMDeviceEnumeration\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AudioPlatformSupport\Windows\WASAPI\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AVEncoder\Private\Decoders\vdecmpeg4;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\AVEncoder\Private\Decoders\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\BinkAudioDecoder\SDK\BinkAudio\Src;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Async\Fundamental;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\ColorManagement\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Containers\Algo;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Experimental\Containers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Experimental\Coroutine;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Experimental\Graph;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Experimental\IO;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Experimental\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Experimental\UnifiedError;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\HAL\Allocators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Internationalization\Cultures;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Modules\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Apple;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Microsoft;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Unix;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\ProfilingDebugging\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Serialization\Csv;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Serialization\Formatters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Tests\HAL;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Core\Private\Tests\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\Misc\DataValidation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\Serialization\Formatters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\CoreUObject\Private\UObject\SavePackage;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Engine\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\DatasmithCore\Private\DirectLink;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\AI\Navigation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Animation\AnimData;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Elements\Actor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Elements\Component;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Elements\Framework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Elements\Interfaces;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Elements\Object;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Elements\SMInstance;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\GameFramework\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\LevelInstance\Test;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Net\Subsystems;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\PhysicsEngine\Experimental;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\PhysicsEngine\ImmediatePhysics;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\PhysicsEngine\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Tests\AutoRTFM;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Tests\Internationalization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Tests\Loading;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Tests\WorldPartition;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\ActorPartition;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\ContentBundle;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\Cook;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\DataLayer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\ErrorHandling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\Filter;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\HLOD;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\Landscape;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\LevelInstance;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\LoaderAdapter;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\NavigationData;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\PackedLevelActor;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\RuntimeHashSet;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\RuntimeSpatialHash;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\WorldPartition\StaticLightingData;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Animation\Constraints\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\ChaosDebugDraw;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\ChaosInsights;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\ChaosVisualDebugger;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Field;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Framework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\GeometryCollection;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\PhysicsProxy;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosSolverEngine\Private\Chaos;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesEngine\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosVisualDebugger\Private\DataWrappers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Private\GeometryCollection;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\GeometryCollectionEngine\Public\GeometryCollection;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\IoStore\HttpClient\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\IoStore\OnDemand\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\IoStore\OnDemandCore\Private;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ISMPool\Private\ISMPool;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ISMPool\Public\ISMPool;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\JsonObjectGraph\Private\JsonObjectGraph;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Voronoi\Private\Voronoi;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\CompGeom\ThirdParty;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\GeometryCore\Private\DynamicMesh\Operations;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\InteractiveToolsFramework\Private\BaseBehaviors\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Interchange\Core\Private\Nodes;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Interchange\Core\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Interchange\Core\Private\Types;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Interchange\Engine\Private\Tasks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\EntitySystem\TrackInstance;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Evaluation\Blending;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Evaluation\Instances;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Evaluation\PreAnimatedState;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieScene\Private\Tests\AutoRTFM;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\MovieSceneTracks\Private\EntitySystem\Interrogation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BackgroundHTTP\Private\GenericPlatform;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BackgroundHTTP\Private\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BackgroundHTTP\Private\PlatformWithModularFeature;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Compactify;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Core;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Data;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Diffing;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Enumeration;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Generation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Installer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Experimental\EventLoopTests\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private\Apple;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private\Curl;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private\GenericPlatform;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private\Interfaces;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private\Unix;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private\WinHttp;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTPServer\Private\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\ICMP\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\SSL\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\SSL\Private\Unix;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\SSL\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Voice\Private\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Voice\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Voice\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Voice\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\WebSockets\Private\Lws;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\WebSockets\Private\WinHttp;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\XMPP\Private\XmppStrophe;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\LauncherPlatform\Private\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\LauncherPlatform\Private\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\LauncherPlatform\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\Proxies\Private\Account;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Portal\Proxies\Private\Application;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RadAudioCodec\SDK\Src\RadA;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\RadAudioCodec\SDK\Src\RadAudio;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Renderer\Private\Substrate\Glint;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Animation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Application;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Commands;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Docking;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Layout;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\MetaData;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\MultiBox;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Notifications;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Styling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Text;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Accessibility;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Colors;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Docking;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Images;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Input;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\LayerManager;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Layout;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Navigation;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Notifications;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Text;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Widgets\Views;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Public\Widgets\Layout;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Widgets\Accessibility;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\SlateCore\Private\Widgets\Images;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TraceLog\Private\Trace\Important;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Private\Elements\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Private\Elements\Framework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Private\Elements\Interfaces;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Tests\Elements\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementFramework\Tests\Elements\Framework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementRuntime\Private\Elements\Framework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TypedElementRuntime\Private\Elements\Interfaces;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Private\Binding\States;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VerseCompiler\Private\uLang\Diagnostics;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VerseCompiler\Private\uLang\Parser;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VerseCompiler\Private\uLang\SemanticAnalyzer;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VerseCompiler\Private\uLang\Semantics;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VerseCompiler\Private\uLang\SourceProject;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VerseCompiler\Private\uLang\Syntax;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\VerseCompiler\Private\uLang\Toolchain;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Windows\D3D11RHI\Private\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\ExtraTests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Eigen\unsupported\doc\examples;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\games-frame-pacing\common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\games-frame-pacing\opengl;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\games-frame-pacing\vulkan;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\src\common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libGPUCounters\Source\src\device;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libGPUCounters\Source\src\hwcpipe;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\Horde\Private\Storage\Bundles\V2;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\ContextSwitches\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\CookProfiler\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\CookProfiler\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler\Tracks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\LoadingProfiler\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler\Tracks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\MemoryProfiler\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\NetworkingProfiler\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\NetworkingProfiler\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\Table\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\Table\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\TaskGraphProfiler\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\TaskGraphProfiler\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\Tests\FunctionalTests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\TimingProfiler\Tracks;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\TimingProfiler\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsights\Private\Insights\TimingProfiler\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Filter\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Filter\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Table\ViewModels;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsCore\Private\InsightsCore\Table\Widgets;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Developer\TraceInsightsFrontend\Private\InsightsFrontend\Tests\FunctionalTests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\PropertyEditor\Private\Tests\DetailsView\PropertyHandle;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\MVVM\ViewModels\OutlinerColumns;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\Sequencer\Private\MVVM\ViewModels\OutlinerIndicators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Private\MVVM\ViewModels\OutlinerColumns;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\SequencerCore\Private\MVVM\Views\OutlinerColumns;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Editor\WorldPartitionEditor\Private\WorldPartition\ContentBundle\Outliner;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Apple\MetalRHI\Private\Shaders\Debugging;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Apple\MetalRHI\Private\Shaders\Types;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Core;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Math;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Topo;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\UI;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Utils;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Engine\Private\TechSoft;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Net\Experimental\Iris;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Net\Iris\ReplicationSystem;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\Net\Tests\Util;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\PhysicsEngine\ImmediatePhysics\ImmediatePhysicsChaos;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Engine\Private\PhysicsEngine\ImmediatePhysics\ImmediatePhysicsPhysX;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Animation\Constraints\Private\Transform;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Character;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Collision;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\DebugDraw;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Deformable;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Evolution;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Framework;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Interface;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Island;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Joint;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Math;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\Chaos\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Chaos\Private\GeometryCollection\Facades;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesCore\Private\SimModule;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Core\Private\Dataflow;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Engine\Private\Dataflow;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Simulation\Private\Dataflow;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\IoStore\OnDemand\Private\Tool;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Stub\Private\Iris;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Common\Private\Net\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Installer\Statistics;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\BuildPatchServices\Private\Tests\Unit;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Experimental\EventLoop\Private\EventLoop;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Experimental\EventLoopTests\Tests\EventLoop;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\HTTP\Private\WinHttp\Support;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\WebSockets\Private\WinHttp\Support;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\MultiBox\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Text\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Slate\Private\Framework\Text\IOS;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Solaris\uLangJSON\Private\uLang\JSON;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Android;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Apple;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Unix;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\TraceLog\Private\Trace\Detail\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\UMG\Private\Binding\States\Tests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\benchmark;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\generators;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\interfaces;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\internal;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\matchers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\reporters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest\helpers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest\IntrospectiveTests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest\TimingTests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\SelfTest\UsageTests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\tests\TestScripts\DiscoverTests;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Eigen\unsupported\doc\examples\SYCL;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\GoogleGameSDK\gamesdk\src\common\jni;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libGPUCounters\Source\src\device\hwcnt;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libGPUCounters\Source\src\hwcpipe\detail;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Programs\LiveLinkHub\Source\LiveLinkHubLauncher\Private\Platform\Linux;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Programs\LiveLinkHub\Source\LiveLinkHubLauncher\Private\Platform\Mac;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Programs\LiveLinkHub\Source\LiveLinkHubLauncher\Private\Platform\Windows;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Geo\Curves;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Geo\Sampling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Geo\Surfaces;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Mesh\Criteria;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Mesh\Meshers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Mesh\Structure;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Dataflow\Simulation\Private\Dataflow\Interfaces;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\FieldSystem\Source\FieldSystemEngine\Private\Field;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\Core;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\DataStream;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationState;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\Stats;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\Analytics;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\Connection;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\DirtyNetObjectTracker;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\NetHandle;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\NetToken;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\PropertyConditions;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\PushModel;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\Serialization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\Trace;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Online\Experimental\EventLoop\Private\EventLoop\BSDSocket;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common\Containers;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common\Memory;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common\Misc;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Solaris\uLangCore\Private\uLang\Common\Text;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\benchmark\detail;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\Catch2\v3.4.0\src\catch2\matchers\internal;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Datasmith\CADKernel\Base\Private\Mesh\Meshers\IsoTriangulator;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\ChunkedDataStream;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\Conditionals;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\DeltaCompression;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\Filtering;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\NetBlob;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\Polling;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Experimental\Iris\Core\Private\Iris\ReplicationSystem\Prioritization;C:\Program Files\Epic Games\UE_5.6\Engine\Source\Runtime\Net\Core\Private\Net\Core\Trace\Reporters;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libGPUCounters\Source\src\device\hwcnt\sampler\detail;C:\Program Files\Epic Games\UE_5.6\Engine\Source\ThirdParty\libGPUCounters\Source\src\device\hwcnt\sampler\kinstr_prfcnt;</SourcePath>
  </PropertyGroup>
  <ItemDefinitionGroup>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <PropertyGroup>
    <CleanDependsOn> $(CleanDependsOn); </CleanDependsOn>
    <CppCleanDependsOn></CppCleanDependsOn>
  </PropertyGroup>
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
