﻿Log file open, 08/26/25 00:08:39
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=2260)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Auracron
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4946] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:\Game\Auracron\Auracron.uproject -compile""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.343374
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-9FA4ED0747B5344F17D6F0B054255B94
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../Game/Auracron/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Unable to find target receipt in path: ../../../../../../Game/Auracron/Binaries/Win64/*.target
LogConfig: Display: Loading Mac ini files took 0.10 seconds
LogConfig: Display: Loading Android ini files took 0.11 seconds
LogConfig: Display: Loading IOS ini files took 0.11 seconds
LogAssetRegistry: Display: Asset registry cache read as 73.1 MiB from ../../../../../../Game/Auracron/Intermediate/CachedAssetRegistry_0.bin.
LogConfig: Display: Loading VulkanPC ini files took 0.12 seconds
LogConfig: Display: Loading Windows ini files took 0.13 seconds
LogConfig: Display: Loading Unix ini files took 0.13 seconds
LogConfig: Display: Loading TVOS ini files took 0.13 seconds
LogConfig: Display: Loading Linux ini files took 0.14 seconds
LogConfig: Display: Loading VisionOS ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Unable to find target receipt in path: ../../../../../../Game/Auracron/Binaries/Win64/*.target
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MassGameplay
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ReplicationGraph
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SmartObjects
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WorldConditions
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin ZoneGraphAnnotations
LogPluginManager: Mounting Engine plugin ZoneGraph
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosVehiclesPlugin
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin FieldSystemPlugin
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin TargetingSystem
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Project plugin UnrealMCP
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
Running C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/Build.bat Development Win64 -Project="C:/Game/Auracron/Auracron.uproject" -TargetType=Editor -Progress -NoEngineChanges -NoHotReloadFromIDE
Using bundled DotNet SDK version: 8.0.300 win-x64
Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" Development Win64 -Project="C:/Game/Auracron/Auracron.uproject" -TargetType=Editor -Progress -NoEngineChanges -NoHotReloadFromIDE
Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log.txt
Available x64 toolchains (1):
 * C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207
    (Family=14.44.35207, FamilyRank=1, Version=14.44.35215, HostArchitecture=x64, ReleaseChannel=Latest, Architecture=x64)
Visual Studio 2022 compiler version 14.44.35215 is not a preferred version. Please use the latest preferred version 14.38.33130
Creating makefile for AuracronEditor (no existing makefile)
@progress push 5%
Parsing headers for AuracronEditor
  Running Internal UnrealHeaderTool C:\Game\Auracron\Auracron.uproject C:\Game\Auracron\Intermediate\Build\Win64\AuracronEditor\Development\AuracronEditor.uhtmanifest -WarningsAsErrors -installed
Total of 6 written
Reflection code generated for AuracronEditor in 6.2369975 seconds
@progress pop
Building AuracronEditor...
Using Visual Studio 2022 14.44.35215 toolchain (C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207) and Windows 10.0.26100.0 SDK (C:\Program Files (x86)\Windows Kits\10).
Warning: Visual Studio 2022 compiler is not a preferred version
[Adaptive Build] Excluded from UnrealMCP unity file: MCPServerRunnable.cpp, UnrealMCPBridge.cpp, UnrealMCPModule.cpp, UnrealMCPAICommands.cpp, UnrealMCPAnalyticsTelemetryCommands.cpp, UnrealMCPAudioSystemCommands.cpp, UnrealMCPBackendServicesCommands.cpp, UnrealMCPBlueprintCommands.cpp, UnrealMCPBlueprintNodeCommands.cpp, UnrealMCPChaosPhysicsCommands.cpp, UnrealMCPCloudServicesCommands.cpp, UnrealMCPCollisionCommands.cpp, UnrealMCPCombatMechanicsCommands.cpp, UnrealMCPCommonUtils.cpp, UnrealMCPEditorCommands.cpp, UnrealMCPGamePhasesCommands.cpp, UnrealMCPHardwareDetectionCommands.cpp, UnrealMCPJungleSystemCommands.cpp, UnrealMCPLaneMechanicsCommands.cpp, UnrealMCPMaterialCommands.cpp, UnrealMCPMultilayerMapCommands.cpp, UnrealMCPNetworkCommands.cpp, UnrealMCPNetworkingCommands.cpp, UnrealMCPNiagaraCommands.cpp, UnrealMCPObjectivesStructuresCommands.cpp, UnrealMCPPathfindingCommands.cpp, UnrealMCPPerformanceCommands.cpp, UnrealMCPPlatformCommands.cpp, UnrealMCPProceduralCommands.cpp, UnrealMCPProjectCommands.cpp, UnrealMCPRealmCommands.cpp, UnrealMCPRenderingPipelineCommands.cpp, UnrealMCPSecurityAntiCheatCommands.cpp, UnrealMCPUMGCommands.cpp, UnrealMCPVerticalNavigationCommands.cpp, UnrealMCPVisionCommands.cpp, UnrealMCPWorldPartitionCommands.cpp
Determining max actions to execute in parallel (10 physical cores, 12 logical cores)
  Executing up to 10 processes, one per physical core
  Requested 1.5 GB memory per action, 11.73 GB available: limiting max parallel actions to 7
Using Unreal Build Accelerator local executor to run 51 action(s)
  Storage capacity 40Gb
---- Starting trace: 250826_001012 ----
UbaServer - Listening on 0.0.0.0:1345
------ Building 51 action(s) started ------
[1/51] Resource Default.rc2
[2/51] Resource Default.rc2
[3/51] Compile [x64] SharedPCH.UnrealEd.Project.ValApi.ValExpApi.Cpp20.cpp
[4/51] Compile [x64] Auracron.cpp
[5/51] Compile [x64] PerModuleInline.gen.cpp
[6/51] Compile [x64] MultilayerManager.cpp
[7/51] Compile [x64] AuracronGameMode.cpp
[8/51] Compile [x64] Module.Auracron.gen.cpp
[9/51] Link [x64] UnrealEditor-Auracron.dll
[10/51] Link [x64] UnrealEditor-Auracron.lib
   Criando biblioteca C:/Game/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/Auracron/UnrealEditor-Auracron.lib e objeto C:/Game/Auracron/Intermediate/Build/Win64/x64/UnrealEditor/Development/Auracron/UnrealEditor-Auracron.exp
[11/51] Compile [x64] UnrealMCPAnalyticsTelemetryCommands.cpp
[12/51] Compile [x64] UnrealMCPBackendServicesCommands.cpp
[13/51] Compile [x64] UnrealMCPBlueprintCommands.cpp
[14/51] Compile [x64] UnrealMCPBlueprintNodeCommands.cpp
[15/51] Compile [x64] UnrealMCPAICommands.cpp
[16/51] Compile [x64] UnrealMCPAudioSystemCommands.cpp
[17/51] Compile [x64] UnrealMCPCloudServicesCommands.cpp
[18/51] Compile [x64] UnrealMCPCombatMechanicsCommands.cpp
[19/51] Compile [x64] UnrealMCPCommonUtils.cpp
[20/51] Compile [x64] UnrealMCPEditorCommands.cpp
[21/51] Compile [x64] UnrealMCPGamePhasesCommands.cpp
[22/51] Compile [x64] UnrealMCPHardwareDetectionCommands.cpp
[23/51] Compile [x64] UnrealMCPCollisionCommands.cpp
[24/51] Compile [x64] UnrealMCPMaterialCommands.cpp
[25/51] Compile [x64] UnrealMCPChaosPhysicsCommands.cpp
[26/51] Compile [x64] UnrealMCPJungleSystemCommands.cpp
[27/51] Compile [x64] UnrealMCPLaneMechanicsCommands.cpp
[28/51] Compile [x64] UnrealMCPNetworkCommands.cpp
[29/51] Compile [x64] Module.UnrealMCP.cpp
[30/51] Compile [x64] UnrealMCPObjectivesStructuresCommands.cpp
[31/51] Compile [x64] MCPServerRunnable.cpp
[32/51] Compile [x64] UnrealMCPMultilayerMapCommands.cpp
[33/51] Compile [x64] UnrealMCPNetworkingCommands.cpp
[34/51] Compile [x64] UnrealMCPPathfindingCommands.cpp
[35/51] Compile [x64] UnrealMCPPerformanceCommands.cpp
[36/51] Compile [x64] UnrealMCPPlatformCommands.cpp
[37/51] Compile [x64] UnrealMCPProjectCommands.cpp
[38/51] Compile [x64] UnrealMCPNiagaraCommands.cpp
[39/51] Compile [x64] UnrealMCPRenderingPipelineCommands.cpp
[40/51] Compile [x64] UnrealMCPSecurityAntiCheatCommands.cpp
[41/51] Compile [x64] UnrealMCPProceduralCommands.cpp
[42/51] Compile [x64] UnrealMCPBridge.cpp
[43/51] Compile [x64] UnrealMCPModule.cpp
[44/51] Compile [x64] UnrealMCPUMGCommands.cpp
