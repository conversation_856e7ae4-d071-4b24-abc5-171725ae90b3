;METADATA=(Diff=true, UseCommands=true)
[/Script/UnrealEd.EditorPerProjectUserSettings]
bDisplayDocumentationLink=False
bDisplayActionListItemRefIds=False
bAlwaysGatherBehaviorTreeDebuggerData=False
bDisplayBlackboardKeysInAlphabeticalOrder=False
bUseSimplygonSwarm=False
SimplygonServerIP=127.0.0.1
bEnableSwarmDebugging=False
SimplygonSwarmDelay=5000
SwarmNumOfConcurrentJobs=16
SwarmMaxUploadChunkSizeInMB=100
SwarmIntermediateFolder=C:/Game/Auracron/Intermediate/Simplygon/
bShowCompilerLogOnCompileError=False
DataSourceFolder=(Path="")
bAnimationReimportWarnings=False
bConfirmEditorClose=False
bSCSEditorShowFloor=False
bAlwaysBuildUAT=True
SCSViewportCameraSpeed=4
bShowSelectionSubcomponents=True
AssetViewerProfileName=
PreviewFeatureLevel=3
PreviewPlatformName=None
PreviewShaderFormatName=None
PreviewShaderPlatformName=None
bPreviewFeatureLevelActive=False
bPreviewFeatureLevelWasDefault=True
PreviewDeviceProfileName=None

[/Script/UnrealEd.EditorStyleSettings]
XAxisColor=(R=0.594000,G=0.019700,B=0.000000,A=1.000000)
YAxisColor=(R=0.134900,G=0.395900,B=0.000000,A=1.000000)
ZAxisColor=(R=0.025100,G=0.207000,B=0.850000,A=1.000000)
ApplicationScale=1.000000
bColorVisionDeficiencyCorrection=False
bColorVisionDeficiencyCorrectionPreviewWithDeficiency=False
SelectionColor=(R=0.828000,G=0.364000,B=0.003000,A=1.000000)
AdditionalSelectionColors[0]=(R=0.019382,G=0.496933,B=1.000000,A=1.000000)
AdditionalSelectionColors[1]=(R=0.356400,G=0.040915,B=0.520996,A=1.000000)
AdditionalSelectionColors[2]=(R=1.000000,G=0.168269,B=0.332452,A=1.000000)
AdditionalSelectionColors[3]=(R=1.000000,G=0.051269,B=0.051269,A=1.000000)
AdditionalSelectionColors[4]=(R=1.000000,G=0.715693,B=0.010330,A=1.000000)
AdditionalSelectionColors[5]=(R=0.258183,G=0.539479,B=0.068478,A=1.000000)
ViewportToolOverlayColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
bEnableEditorWindowBackgroundColor=False
EditorWindowBackgroundColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
MenuSearchFieldVisibilityThreshold=10
bUseGrid=True
bAntiAliasGrid=True
RegularColor=(R=0.024000,G=0.024000,B=0.024000,A=1.000000)
RuleColor=(R=0.010000,G=0.010000,B=0.010000,A=1.000000)
CenterColor=(R=0.005000,G=0.005000,B=0.005000,A=1.000000)
GridSnapSize=16
GraphBackgroundBrush=(TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),ResourceObject=None,OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=0.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False),bIsDynamicallyLoaded=False,ResourceName="")
bShowNativeComponentNames=True
AssetEditorOpenLocation=Default
bEnableColorizedEditorTabs=True
CurrentAppliedTheme=134380265FBB4A9CA00A1DC9770217B8
bEnableMiddleEllipsis=True

[/Script/UnrealEd.LevelEditorPlaySettings]
LaptopScreenResolutions=(Description="Apple MacBook Air 11",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Air 13\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\"",Width=1280,Height=800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\" (Retina)",Width=2560,Height=1600,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\" (Retina)",Width=2880,Height=1800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Generic 14-15.6\" Notebook",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="19\" monitor",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="20\" monitor",Width=1600,Height=900,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="22\" monitor",Width=1680,Height=1050,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="21.5-24\" monitor",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="27\" monitor",Width=2560,Height=1440,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (3rd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro3_129")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (2nd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro2_129")
TabletScreenResolutions=(Description="iPad Pro 11-inch",Width=834,Height=1194,AspectRatio="5:7",bCanSwapAspectRatio=True,ProfileName="iPadPro11")
TabletScreenResolutions=(Description="iPad Pro 10.5-inch",Width=834,Height=1112,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro105")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch",Width=1024,Height=1366,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro129")
TabletScreenResolutions=(Description="iPad Pro 9.7-inch",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro97")
TabletScreenResolutions=(Description="iPad (6th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad6")
TabletScreenResolutions=(Description="iPad (5th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad5")
TabletScreenResolutions=(Description="iPad Air 3",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir3")
TabletScreenResolutions=(Description="iPad Air 2",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir2")
TabletScreenResolutions=(Description="iPad Mini 5",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini5")
TabletScreenResolutions=(Description="iPad Mini 4",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini4")
TabletScreenResolutions=(Description="LG G Pad X 8.0",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Asus Zenpad 3s 10",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Huawei MediaPad M3",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface RT",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface Pro",Width=1080,Height=1920,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="720p (HDTV, Blu-ray)",Width=1280,Height=720,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="1080i, 1080p (HDTV, Blu-ray)",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Ultra HD",Width=3840,Height=2160,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Digital Cinema",Width=4096,Height=2160,AspectRatio="1.90:1",bCanSwapAspectRatio=True,ProfileName="")
GameGetsMouseControl=False
UseMouseForTouch=False
MouseControlLabelPosition=LabelAnchorMode_TopLeft
ViewportGetsHMDControl=False
bShouldMinimizeEditorOnNonVRPIE=False
bEmulateStereo=False
SoloAudioInFirstPIEClient=False
EnablePIEEnterAndExitSounds=False
PlayInEditorSoundQualityLevel=0
bUseNonRealtimeAudioDevice=False
bPreferToStreamLevelsInPIE=False
bPromoteOutputLogWarningsDuringPIE=False
NewWindowPosition=(X=-1,Y=-1)
PIEAlwaysOnTop=False
DisableStandaloneSound=False
AdditionalLaunchParameters=
BuildGameBeforeLaunch=PlayOnBuild_Default
LaunchConfiguration=LaunchConfig_Default
PackFilesForLaunch=NoPak
bAutoCompileBlueprintsOnLaunch=True
bLaunchSeparateServer=False
PlayNetMode=PIE_Standalone
RunUnderOneProcess=True
PlayNumberOfClients=1
PrimaryPIEClientIndex=0
ServerPort=17777
ClientWindowWidth=640
RouteGamepadToSecondWindow=False
CreateAudioDeviceForEveryPlayer=False
ClientWindowHeight=480
ServerMapNameOverride=
AdditionalServerGameOptions=
bShowServerDebugDrawingByDefault=True
ServerDebugDrawingColorTintStrength=0.000000
ServerDebugDrawingColorTint=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)
bHMDForPrimaryProcessOnly=True
AdditionalServerLaunchParameters=
ServerFixedFPS=0
NetworkEmulationSettings=(bIsNetworkEmulationEnabled=False,EmulationTarget=Server,CurrentProfile="Custom",OutPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0),InPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0))
LastSize=(X=0,Y=0)
LastExecutedLaunchDevice=Windows@TKT
LastExecutedLaunchName=TKT
LastExecutedPIEPreviewDevice=
DeviceToEmulate=
PIESafeZoneOverride=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000)

[/Script/UnrealEd.LevelEditorViewportSettings]
FlightCameraControlExperimentalNavigation=False
MinimumOrthographicZoom=250.000000
bAllowArcballRotate=False
bAllowScreenRotate=False
bShowActorEditorContext=True
bShowBrushMarkerPolys=False
bAllowEditWidgetAxisDisplay=True
bUseLegacyCameraMovementNotifications=False
SnapToSurface=(bEnabled=False,SnapOffsetExtent=0.000000,bSnapRotation=True)
bEnableLayerSnap=False
ActiveSnapLayerIndex=0
PreserveNonUniformScale=True
bUseLODViewLocking=False
PreviewMeshes=/Engine/EditorMeshes/ColorCalibrator/SM_ColorCalibrator.SM_ColorCalibrator
BillboardScale=1.000000
bSaveEngineStats=False
MeasuringToolUnits=MeasureUnits_Centimeters
SelectedSplinePointSizeAdjustment=0.000000
SplineLineThicknessAdjustment=0.000000
SplineTangentHandleSizeAdjustment=0.000000
SplineTangentScale=0.500000
LastInViewportMenuLocation=(X=0.000000,Y=0.000000)
MaterialForDroppedTextures=None
MaterialParamsForDroppedTextures=()
EditorViews=(("/Temp/Untitled_1.Untitled_1", (LevelViewportsInfo=((),(CamUpdated=True),(CamUpdated=True),(CamPosition=(X=75201.914251,Y=85553.025721,Z=44620.977240),CamRotation=(Pitch=-24.199900,Yaw=-854.398190,Roll=0.000000)),(CamUpdated=True),(CamUpdated=True),(),()))),("/Game/AURACRON.AURACRON", (LevelViewportsInfo=((),(),(),(CamPosition=(X=2774.976239,Y=-0.000059,Z=743.552655),CamRotation=(Pitch=-20.800000,Yaw=-194.599997,Roll=-0.000001)),(),(),(),()))))
PropertyColorationColorForMatchingObjects=(B=0,G=0,R=255,A=255)
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport0",ConfigSettings=(ViewportType=LVT_OrthoNegativeYZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport1",ConfigSettings=(ViewportType=LVT_Perspective,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=1,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=1,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=True,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport2",ConfigSettings=(ViewportType=LVT_OrthoNegativeXZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport3",ConfigSettings=(ViewportType=LVT_OrthoXY,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeDebugPrimitives=1,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalFogVolumes=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,MaterialNormal=1,MaterialAmbientOcclusion=1,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeDebugPrimitives=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,UnlitRichView=1,UnlitViewmode=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VisualizeVirtualTexture=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1,AllowPrimitiveAlphaHoldout=1,AlphaInvert=0,SmartObjects=0,GameplayCameras=1,PCGDebug=1,ZoneGraph=0",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",VirtualTextureVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))

[MRU]
MRUItem0=/Game/AURACRON

[DetailCustomWidgetExpansion]
WorldSettings=WorldSettings.WorldPartitionSetup.RuntimeHash
WorldPartitionRuntimeHashSet=WorldSettings.WorldPartitionSetup.RuntimeHash

[EditorStartup]
LastLevel=/Engine/Maps/Templates/OpenWorld

[DetailPropertyExpansion]
WorldPartitionRuntimeHashSet="\"Object\" "
WorldPartitionRuntimeHash="\"Object\" "
Object="\"Object\" "
Actor="\"Object\" "
SceneComponent="\"Object\" "
ActorComponent="\"Object\" "

[DetailCategories]
WorldPartitionRuntimeHashSet.RuntimeSettings=True
WorldSettings.WorldPartitionSetup=True
WorldSettings.GameMode=True
WorldSettings.Physics=True
WorldSettings.Lightmass=True
WorldSettings.World=True
WorldSettings.VR=True
WorldSettings.LightmassVolumeLighting=True
WorldSettings.PrecomputedVisibility=True
WorldSettings.Broadphase=True
WorldSettings.Network=True
WorldSettings.Foliage=True
WorldSettings.Landscape=True
WorldSettings.Navigation=True
WorldSettings.Rendering=True
WorldSettings.Nanite=True
WorldSettings.Audio=True
WorldSettings.Tick=True
WorldSettings.Bookmark=True
WorldSettings.Networking=True
WorldSettings.HLODSystem=True

[AssetEditorSubsystem]
CleanShutdown=True
DebuggerAttached=False

[WindowsEditor]
SuppressMissingAdvancedRenderingRequirementsNotification=True

[RootWindow]
ScreenPosition=X=320.000 Y=149.600
WindowSize=X=1294.400 Y=734.400
InitiallyMaximized=True

[SlateAdditionalLayoutConfig]
Viewport 1.LayoutType=FourPanes2x2
FourPanes2x2.Viewport 1.Percentages0=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages1=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages2=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages3=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Viewport0.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport1.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport2.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport3.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.bIsMaximized=True
FourPanes2x2.Viewport 1.MaximizedViewport=FourPanes2x2.Viewport 1.Viewport1

[Directories2]
UNR=../../../../../../Game/Auracron/Content
BRUSH=../../../../../../Game/Auracron/Content/
FBX=../../../../../../Game/Auracron/Content/
FBXAnim=../../../../../../Game/Auracron/Content/
GenericImport=../../../../../../Game/Auracron/Content/
GenericExport=../../../../../../Game/Auracron/Content/
GenericOpen=../../../../../../Game/Auracron/Content/
GenericSave=../../../../../../Game/Auracron/Content/
MeshImportExport=../../../../../../Game/Auracron/Content/
WorldRoot=../../../../../../Game/Auracron/Content/
Level=../../../../../../Game/Auracron/Content
Project=C:/Program Files/Epic Games/UE_5.6/

[/Script/RewindDebuggerVLog.RewindDebuggerVLogSettings]
DisplayVerbosity=4
DisplayCategories=()

[/Script/RewindDebugger.RewindDebuggerSettings]
CameraMode=Replay
bShouldAutoEject=False
bShouldAutoRecordOnPIE=False
PlaybackRate=1.000000
bShowEmptyObjectTracks=False
DebugTargetActor=

[ModuleFileTracking]
StorageServerClient.TimeStamp=2025.08.22-14.44.35
StorageServerClient.LastCompileMethod=Unknown
CookOnTheFly.TimeStamp=2025.08.22-14.44.00
CookOnTheFly.LastCompileMethod=Unknown
StreamingFile.TimeStamp=2025.08.22-14.44.35
StreamingFile.LastCompileMethod=Unknown
NetworkFile.TimeStamp=2025.08.22-14.44.23
NetworkFile.LastCompileMethod=Unknown
PakFile.TimeStamp=2025.08.22-14.44.24
PakFile.LastCompileMethod=Unknown
RSA.TimeStamp=2025.08.22-14.44.30
RSA.LastCompileMethod=Unknown
SandboxFile.TimeStamp=2025.08.22-14.44.30
SandboxFile.LastCompileMethod=Unknown
CoreUObject.TimeStamp=2025.08.22-14.44.02
CoreUObject.LastCompileMethod=Unknown
Engine.TimeStamp=2025.08.22-14.44.13
Engine.LastCompileMethod=Unknown
UniversalObjectLocator.TimeStamp=2025.08.22-14.44.38
UniversalObjectLocator.LastCompileMethod=Unknown
Renderer.TimeStamp=2025.08.22-14.44.30
Renderer.LastCompileMethod=Unknown
AnimGraphRuntime.TimeStamp=2025.08.22-14.43.54
AnimGraphRuntime.LastCompileMethod=Unknown
SlateRHIRenderer.TimeStamp=2025.08.22-14.44.34
SlateRHIRenderer.LastCompileMethod=Unknown
Landscape.TimeStamp=2025.08.22-14.44.18
Landscape.LastCompileMethod=Unknown
RHICore.TimeStamp=2025.08.22-14.44.30
RHICore.LastCompileMethod=Unknown
RenderCore.TimeStamp=2025.08.22-14.44.27
RenderCore.LastCompileMethod=Unknown
TextureCompressor.TimeStamp=2025.08.22-14.44.35
TextureCompressor.LastCompileMethod=Unknown
OpenColorIOWrapper.TimeStamp=2025.08.22-14.44.24
OpenColorIOWrapper.LastCompileMethod=Unknown
Virtualization.TimeStamp=2025.08.22-14.44.41
Virtualization.LastCompileMethod=Unknown
MessageLog.TimeStamp=2025.08.22-14.44.22
MessageLog.LastCompileMethod=Unknown
AudioEditor.TimeStamp=2025.08.22-14.43.54
AudioEditor.LastCompileMethod=Unknown
PropertyEditor.TimeStamp=2025.08.22-14.44.27
PropertyEditor.LastCompileMethod=Unknown
AudioExtensions.TimeStamp=2025.08.22-14.43.54
AudioExtensions.LastCompileMethod=Unknown
AndroidAudioFeatures.TimeStamp=
AndroidAudioFeatures.LastCompileMethod=Unknown
IOSAudioFeatures.TimeStamp=
IOSAudioFeatures.LastCompileMethod=Unknown
LinuxAudioFeatures.TimeStamp=
LinuxAudioFeatures.LastCompileMethod=Unknown
MacAudioFeatures.TimeStamp=
MacAudioFeatures.LastCompileMethod=Unknown
TVOSAudioFeatures.TimeStamp=
TVOSAudioFeatures.LastCompileMethod=Unknown
VisionOSAudioFeatures.TimeStamp=
VisionOSAudioFeatures.LastCompileMethod=Unknown
WindowsAudioFeatures.TimeStamp=
WindowsAudioFeatures.LastCompileMethod=Unknown
SignalProcessing.TimeStamp=2025.08.22-14.44.32
SignalProcessing.LastCompileMethod=Unknown
AudioMixerCore.TimeStamp=2025.08.22-14.43.55
AudioMixerCore.LastCompileMethod=Unknown
AnimationModifiers.TimeStamp=2025.08.22-14.43.53
AnimationModifiers.LastCompileMethod=Unknown
IoStoreOnDemandCore.TimeStamp=2025.08.22-14.44.16
IoStoreOnDemandCore.LastCompileMethod=Unknown
OpusAudioDecoder.TimeStamp=2025.08.22-14.44.24
OpusAudioDecoder.LastCompileMethod=Unknown
VorbisAudioDecoder.TimeStamp=2025.08.22-14.44.42
VorbisAudioDecoder.LastCompileMethod=Unknown
AdpcmAudioDecoder.TimeStamp=2025.08.22-14.43.52
AdpcmAudioDecoder.LastCompileMethod=Unknown
BinkAudioDecoder.TimeStamp=2025.08.22-14.43.55
BinkAudioDecoder.LastCompileMethod=Unknown
RadAudioDecoder.TimeStamp=2025.08.22-14.44.27
RadAudioDecoder.LastCompileMethod=Unknown
TelemetryUtils.TimeStamp=2025.08.22-14.44.35
TelemetryUtils.LastCompileMethod=Unknown
FastBuildController.TimeStamp=2025.08.22-14.54.37
FastBuildController.LastCompileMethod=Unknown
UbaController.TimeStamp=2025.08.22-14.56.30
UbaController.LastCompileMethod=Unknown
XGEController.TimeStamp=2025.08.22-14.56.50
XGEController.LastCompileMethod=Unknown
PlasticSourceControl.TimeStamp=2025.08.22-14.53.40
PlasticSourceControl.LastCompileMethod=Unknown
SourceControl.TimeStamp=2025.08.22-14.44.34
SourceControl.LastCompileMethod=Unknown
PerforceSourceControl.TimeStamp=2025.08.22-14.53.39
PerforceSourceControl.LastCompileMethod=Unknown
PlatformCrypto.TimeStamp=2025.08.22-14.54.27
PlatformCrypto.LastCompileMethod=Unknown
PlatformCryptoTypes.TimeStamp=2025.08.22-14.54.28
PlatformCryptoTypes.LastCompileMethod=Unknown
PlatformCryptoContext.TimeStamp=2025.08.22-14.54.28
PlatformCryptoContext.LastCompileMethod=Unknown
PythonScriptPluginPreload.TimeStamp=2025.08.22-14.54.28
PythonScriptPluginPreload.LastCompileMethod=Unknown
DesktopPlatform.TimeStamp=2025.08.22-14.44.04
DesktopPlatform.LastCompileMethod=Unknown
ChaosCloth.TimeStamp=2025.08.22-14.53.30
ChaosCloth.LastCompileMethod=Unknown
EOSShared.TimeStamp=2025.08.22-14.55.21
EOSShared.LastCompileMethod=Unknown
OnlineServicesInterface.TimeStamp=2025.08.22-14.55.22
OnlineServicesInterface.LastCompileMethod=Unknown
OnlineServicesCommon.TimeStamp=2025.08.22-14.55.22
OnlineServicesCommon.LastCompileMethod=Unknown
OnlineServicesCommonEngineUtils.TimeStamp=2025.08.22-14.55.22
OnlineServicesCommonEngineUtils.LastCompileMethod=Unknown
OnlineSubsystem.TimeStamp=2025.08.22-14.55.24
OnlineSubsystem.LastCompileMethod=Unknown
HTTP.TimeStamp=2025.08.22-14.44.15
HTTP.LastCompileMethod=Unknown
SSL.TimeStamp=2025.08.22-14.44.35
SSL.LastCompileMethod=Unknown
XMPP.TimeStamp=2025.08.22-14.44.43
XMPP.LastCompileMethod=Unknown
WebSockets.TimeStamp=2025.08.22-14.44.42
WebSockets.LastCompileMethod=Unknown
OnlineSubsystemNULL.TimeStamp=2025.08.22-14.55.25
OnlineSubsystemNULL.LastCompileMethod=Unknown
Sockets.TimeStamp=2025.08.22-14.44.34
Sockets.LastCompileMethod=Unknown
OnlineSubsystemUtils.TimeStamp=2025.08.22-14.55.25
OnlineSubsystemUtils.LastCompileMethod=Unknown
OnlineBlueprintSupport.TimeStamp=2025.08.22-14.55.25
OnlineBlueprintSupport.LastCompileMethod=Unknown
LauncherChunkInstaller.TimeStamp=2025.08.22-14.55.37
LauncherChunkInstaller.LastCompileMethod=Unknown
CameraCalibrationCore.TimeStamp=2025.08.22-14.56.36
CameraCalibrationCore.LastCompileMethod=Unknown
AISupportModule.TimeStamp=2025.08.22-14.53.07
AISupportModule.LastCompileMethod=Unknown
ACLPlugin.TimeStamp=2025.08.22-14.53.09
ACLPlugin.LastCompileMethod=Unknown
OptimusSettings.TimeStamp=2025.08.22-14.53.15
OptimusSettings.LastCompileMethod=Unknown
RenderDocPlugin.TimeStamp=2025.08.22-14.53.40
RenderDocPlugin.LastCompileMethod=Unknown
PixWinPlugin.TimeStamp=2025.08.22-14.53.40
PixWinPlugin.LastCompileMethod=Unknown
GLTFExporter.TimeStamp=2025.08.22-14.53.53
GLTFExporter.LastCompileMethod=Unknown
DatasmithContent.TimeStamp=2025.08.22-14.53.52
DatasmithContent.LastCompileMethod=Unknown
VariantManagerContent.TimeStamp=2025.08.22-14.53.56
VariantManagerContent.LastCompileMethod=Unknown
EditorTelemetry.TimeStamp=2025.08.22-14.54.10
EditorTelemetry.LastCompileMethod=Unknown
Analytics.TimeStamp=2025.08.22-14.43.53
Analytics.LastCompileMethod=Unknown
EditorPerformance.TimeStamp=2025.08.22-14.54.10
EditorPerformance.LastCompileMethod=Unknown
StallLogSubsystem.TimeStamp=2025.08.22-14.54.10
StallLogSubsystem.LastCompileMethod=Unknown
NFORDenoise.TimeStamp=2025.08.22-14.54.23
NFORDenoise.LastCompileMethod=Unknown
RuntimeTelemetry.TimeStamp=2025.08.22-14.54.30
RuntimeTelemetry.LastCompileMethod=Unknown
NiagaraShader.TimeStamp=2025.08.22-14.54.41
NiagaraShader.LastCompileMethod=Unknown
NiagaraVertexFactories.TimeStamp=2025.08.22-14.54.41
NiagaraVertexFactories.LastCompileMethod=Unknown
InterchangeAssets.TimeStamp=2025.08.22-14.54.47
InterchangeAssets.LastCompileMethod=Unknown
ExrReaderGpu.TimeStamp=2025.08.22-14.54.55
ExrReaderGpu.LastCompileMethod=Unknown
WmfMedia.TimeStamp=2025.08.22-14.55.02
WmfMedia.LastCompileMethod=Unknown
Media.TimeStamp=2025.08.22-14.44.21
Media.LastCompileMethod=Unknown
NNEDenoiserShaders.TimeStamp=2025.08.22-14.55.20
NNEDenoiserShaders.LastCompileMethod=Unknown
ComputeFramework.TimeStamp=2025.08.22-14.55.41
ComputeFramework.LastCompileMethod=Unknown
ChunkDownloader.TimeStamp=2025.08.22-14.55.40
ChunkDownloader.LastCompileMethod=Unknown
ExampleDeviceProfileSelector.TimeStamp=2025.08.22-14.55.43
ExampleDeviceProfileSelector.LastCompileMethod=Unknown
HairStrandsCore.TimeStamp=2025.08.22-14.55.50
HairStrandsCore.LastCompileMethod=Unknown
WindowsDeviceProfileSelector.TimeStamp=2025.08.22-14.56.26
WindowsDeviceProfileSelector.LastCompileMethod=Unknown
CompositeCore.TimeStamp=2025.08.22-14.54.08
CompositeCore.LastCompileMethod=Unknown
D3D12RHI.TimeStamp=2025.08.22-14.44.03
D3D12RHI.LastCompileMethod=Unknown
D3D11RHI.TimeStamp=2025.08.22-14.44.03
D3D11RHI.LastCompileMethod=Unknown
WindowsPlatformFeatures.TimeStamp=2025.08.22-14.44.42
WindowsPlatformFeatures.LastCompileMethod=Unknown
GameplayMediaEncoder.TimeStamp=2025.08.22-14.44.13
GameplayMediaEncoder.LastCompileMethod=Unknown
AVEncoder.TimeStamp=2025.08.22-14.43.55
AVEncoder.LastCompileMethod=Unknown
Chaos.TimeStamp=2025.08.22-14.43.58
Chaos.LastCompileMethod=Unknown
GeometryCore.TimeStamp=2025.08.22-14.44.14
GeometryCore.LastCompileMethod=Unknown
ChaosSolverEngine.TimeStamp=2025.08.22-14.43.58
ChaosSolverEngine.LastCompileMethod=Unknown
DirectoryWatcher.TimeStamp=2025.08.22-14.44.06
DirectoryWatcher.LastCompileMethod=Unknown
Settings.TimeStamp=2025.08.22-14.44.32
Settings.LastCompileMethod=Unknown
InputCore.TimeStamp=2025.08.22-14.44.15
InputCore.LastCompileMethod=Unknown
TargetPlatform.TimeStamp=2025.08.22-14.44.35
TargetPlatform.LastCompileMethod=Unknown
TurnkeySupport.TimeStamp=2025.08.22-14.44.37
TurnkeySupport.LastCompileMethod=Unknown
TextureFormat.TimeStamp=2025.08.22-14.44.36
TextureFormat.LastCompileMethod=Unknown
TextureFormatASTC.TimeStamp=2025.08.22-14.44.36
TextureFormatASTC.LastCompileMethod=Unknown
TextureFormatDXT.TimeStamp=2025.08.22-14.44.36
TextureFormatDXT.LastCompileMethod=Unknown
TextureFormatETC2.TimeStamp=2025.08.22-14.44.36
TextureFormatETC2.LastCompileMethod=Unknown
TextureFormatIntelISPCTexComp.TimeStamp=2025.08.22-14.44.36
TextureFormatIntelISPCTexComp.LastCompileMethod=Unknown
TextureFormatUncompressed.TimeStamp=2025.08.22-14.44.36
TextureFormatUncompressed.LastCompileMethod=Unknown
TextureFormatOodle.TimeStamp=2025.08.22-14.53.41
TextureFormatOodle.LastCompileMethod=Unknown
AndroidTargetPlatform.TimeStamp=2025.08.22-14.43.23
AndroidTargetPlatform.LastCompileMethod=Unknown
AndroidTargetPlatformSettings.TimeStamp=2025.08.22-14.43.23
AndroidTargetPlatformSettings.LastCompileMethod=Unknown
AndroidTargetPlatformControls.TimeStamp=2025.08.22-14.43.23
AndroidTargetPlatformControls.LastCompileMethod=Unknown
IOSTargetPlatform.TimeStamp=2025.08.22-14.43.34
IOSTargetPlatform.LastCompileMethod=Unknown
IOSTargetPlatformSettings.TimeStamp=2025.08.22-14.43.34
IOSTargetPlatformSettings.LastCompileMethod=Unknown
IOSTargetPlatformControls.TimeStamp=2025.08.22-14.43.34
IOSTargetPlatformControls.LastCompileMethod=Unknown
LinuxTargetPlatform.TimeStamp=2025.08.22-14.43.34
LinuxTargetPlatform.LastCompileMethod=Unknown
LinuxTargetPlatformSettings.TimeStamp=2025.08.22-14.43.34
LinuxTargetPlatformSettings.LastCompileMethod=Unknown
LinuxTargetPlatformControls.TimeStamp=2025.08.22-14.43.34
LinuxTargetPlatformControls.LastCompileMethod=Unknown
MacTargetPlatform.TimeStamp=2025.08.22-14.44.20
MacTargetPlatform.LastCompileMethod=Unknown
MacTargetPlatformSettings.TimeStamp=2025.08.22-14.44.20
MacTargetPlatformSettings.LastCompileMethod=Unknown
MacTargetPlatformControls.TimeStamp=2025.08.22-14.44.20
MacTargetPlatformControls.LastCompileMethod=Unknown
TVOSTargetPlatform.TimeStamp=2025.08.22-14.43.34
TVOSTargetPlatform.LastCompileMethod=Unknown
TVOSTargetPlatformSettings.TimeStamp=2025.08.22-14.43.34
TVOSTargetPlatformSettings.LastCompileMethod=Unknown
TVOSTargetPlatformControls.TimeStamp=2025.08.22-14.43.34
TVOSTargetPlatformControls.LastCompileMethod=Unknown
WindowsTargetPlatform.TimeStamp=2025.08.22-14.44.42
WindowsTargetPlatform.LastCompileMethod=Unknown
WindowsTargetPlatformSettings.TimeStamp=2025.08.22-14.44.42
WindowsTargetPlatformSettings.LastCompileMethod=Unknown
WindowsTargetPlatformControls.TimeStamp=2025.08.22-14.44.42
WindowsTargetPlatformControls.LastCompileMethod=Unknown
AudioFormatOPUS.TimeStamp=2025.08.22-14.43.55
AudioFormatOPUS.LastCompileMethod=Unknown
AudioFormatOGG.TimeStamp=2025.08.22-14.43.55
AudioFormatOGG.LastCompileMethod=Unknown
AudioFormatADPCM.TimeStamp=2025.08.22-14.43.55
AudioFormatADPCM.LastCompileMethod=Unknown
AudioFormatBINK.TimeStamp=2025.08.22-14.43.55
AudioFormatBINK.LastCompileMethod=Unknown
AudioFormatRAD.TimeStamp=2025.08.22-14.43.55
AudioFormatRAD.LastCompileMethod=Unknown
ShaderFormatVectorVM.TimeStamp=2025.08.22-14.44.32
ShaderFormatVectorVM.LastCompileMethod=Unknown
ShaderFormatD3D.TimeStamp=2025.08.22-14.44.32
ShaderFormatD3D.LastCompileMethod=Unknown
ShaderFormatOpenGL.TimeStamp=2025.08.22-14.44.32
ShaderFormatOpenGL.LastCompileMethod=Unknown
VulkanShaderFormat.TimeStamp=2025.08.22-14.44.42
VulkanShaderFormat.LastCompileMethod=Unknown
MetalShaderFormat.TimeStamp=2025.08.22-14.44.22
MetalShaderFormat.LastCompileMethod=Unknown
DerivedDataCache.TimeStamp=2025.08.22-14.44.04
DerivedDataCache.LastCompileMethod=Unknown
ShaderPreprocessor.TimeStamp=2025.08.22-14.44.32
ShaderPreprocessor.LastCompileMethod=Unknown
ImageWrapper.TimeStamp=2025.08.22-14.44.15
ImageWrapper.LastCompileMethod=Unknown
NullInstallBundleManager.TimeStamp=2025.08.22-14.44.24
NullInstallBundleManager.LastCompileMethod=Unknown
AssetRegistry.TimeStamp=2025.08.22-14.43.54
AssetRegistry.LastCompileMethod=Unknown
TargetDeviceServices.TimeStamp=2025.08.22-14.44.35
TargetDeviceServices.LastCompileMethod=Unknown
MeshUtilities.TimeStamp=2025.08.22-14.44.21
MeshUtilities.LastCompileMethod=Unknown
MaterialBaking.TimeStamp=2025.08.22-14.44.20
MaterialBaking.LastCompileMethod=Unknown
MeshMergeUtilities.TimeStamp=2025.08.22-14.44.21
MeshMergeUtilities.LastCompileMethod=Unknown
MeshReductionInterface.TimeStamp=2025.08.22-14.44.21
MeshReductionInterface.LastCompileMethod=Unknown
QuadricMeshReduction.TimeStamp=2025.08.22-14.44.27
QuadricMeshReduction.LastCompileMethod=Unknown
ProxyLODMeshReduction.TimeStamp=2025.08.22-14.53.45
ProxyLODMeshReduction.LastCompileMethod=Unknown
SkeletalMeshReduction.TimeStamp=2025.08.22-14.54.31
SkeletalMeshReduction.LastCompileMethod=Unknown
MeshBoneReduction.TimeStamp=2025.08.22-14.44.21
MeshBoneReduction.LastCompileMethod=Unknown
StaticMeshDescription.TimeStamp=2025.08.22-14.44.35
StaticMeshDescription.LastCompileMethod=Unknown
GeometryProcessingInterfaces.TimeStamp=2025.08.22-14.44.14
GeometryProcessingInterfaces.LastCompileMethod=Unknown
NaniteBuilder.TimeStamp=2025.08.22-14.44.23
NaniteBuilder.LastCompileMethod=Unknown
MeshBuilder.TimeStamp=2025.08.22-14.44.21
MeshBuilder.LastCompileMethod=Unknown
KismetCompiler.TimeStamp=2025.08.22-14.44.18
KismetCompiler.LastCompileMethod=Unknown
MovieSceneTools.TimeStamp=2025.08.22-14.44.22
MovieSceneTools.LastCompileMethod=Unknown
Sequencer.TimeStamp=2025.08.22-14.44.31
Sequencer.LastCompileMethod=Unknown
CurveEditor.TimeStamp=2025.08.22-14.44.02
CurveEditor.LastCompileMethod=Unknown
SequencerCore.TimeStamp=2025.08.22-14.44.31
SequencerCore.LastCompileMethod=Unknown
AssetDefinition.TimeStamp=2025.08.22-14.43.54
AssetDefinition.LastCompileMethod=Unknown
Core.TimeStamp=2025.08.22-14.44.01
Core.LastCompileMethod=Unknown
Networking.TimeStamp=2025.08.22-14.44.23
Networking.LastCompileMethod=Unknown
LiveCoding.TimeStamp=2025.08.22-14.44.19
LiveCoding.LastCompileMethod=Unknown
HeadMountedDisplay.TimeStamp=2025.08.22-14.44.14
HeadMountedDisplay.LastCompileMethod=Unknown
SourceCodeAccess.TimeStamp=2025.08.22-14.44.34
SourceCodeAccess.LastCompileMethod=Unknown
Messaging.TimeStamp=2025.08.22-14.44.22
Messaging.LastCompileMethod=Unknown
MRMesh.TimeStamp=2025.08.22-14.44.23
MRMesh.LastCompileMethod=Unknown
UnrealEd.TimeStamp=2025.08.22-14.44.41
UnrealEd.LastCompileMethod=Unknown
LandscapeEditorUtilities.TimeStamp=2025.08.22-14.44.18
LandscapeEditorUtilities.LastCompileMethod=Unknown
SubobjectDataInterface.TimeStamp=2025.08.22-14.44.35
SubobjectDataInterface.LastCompileMethod=Unknown
SlateCore.TimeStamp=2025.08.22-14.44.33
SlateCore.LastCompileMethod=Unknown
Slate.TimeStamp=2025.08.22-14.44.33
Slate.LastCompileMethod=Unknown
SlateReflector.TimeStamp=2025.08.22-14.44.34
SlateReflector.LastCompileMethod=Unknown
EditorStyle.TimeStamp=2025.08.22-14.44.06
EditorStyle.LastCompileMethod=Unknown
UMG.TimeStamp=2025.08.22-14.44.38
UMG.LastCompileMethod=Unknown
UMGEditor.TimeStamp=2025.08.22-14.44.38
UMGEditor.LastCompileMethod=Unknown
AssetTools.TimeStamp=2025.08.22-14.43.54
AssetTools.LastCompileMethod=Unknown
ScriptableEditorWidgets.TimeStamp=2025.08.22-14.44.30
ScriptableEditorWidgets.LastCompileMethod=Unknown
CollisionAnalyzer.TimeStamp=2025.08.22-14.43.59
CollisionAnalyzer.LastCompileMethod=Unknown
WorkspaceMenuStructure.TimeStamp=2025.08.22-14.44.42
WorkspaceMenuStructure.LastCompileMethod=Unknown
FunctionalTesting.TimeStamp=2025.08.22-14.44.13
FunctionalTesting.LastCompileMethod=Unknown
BehaviorTreeEditor.TimeStamp=2025.08.22-14.43.55
BehaviorTreeEditor.LastCompileMethod=Unknown
GameplayTasksEditor.TimeStamp=2025.08.22-14.44.13
GameplayTasksEditor.LastCompileMethod=Unknown
StringTableEditor.TimeStamp=2025.08.22-14.44.35
StringTableEditor.LastCompileMethod=Unknown
VREditor.TimeStamp=2025.08.22-14.44.42
VREditor.LastCompileMethod=Unknown
MaterialEditor.TimeStamp=2025.08.22-14.44.21
MaterialEditor.LastCompileMethod=Unknown
Overlay.TimeStamp=2025.08.22-14.44.24
Overlay.LastCompileMethod=Unknown
OverlayEditor.TimeStamp=2025.08.22-14.44.24
OverlayEditor.LastCompileMethod=Unknown
MediaAssets.TimeStamp=2025.08.22-14.44.21
MediaAssets.LastCompileMethod=Unknown
ClothingSystemRuntimeNv.TimeStamp=2025.08.22-14.43.59
ClothingSystemRuntimeNv.LastCompileMethod=Unknown
ClothingSystemEditor.TimeStamp=2025.08.22-14.43.59
ClothingSystemEditor.LastCompileMethod=Unknown
AnimationDataController.TimeStamp=2025.08.22-14.43.53
AnimationDataController.LastCompileMethod=Unknown
TimeManagement.TimeStamp=2025.08.22-14.44.36
TimeManagement.LastCompileMethod=Unknown
AnimGraph.TimeStamp=2025.08.22-14.43.53
AnimGraph.LastCompileMethod=Unknown
WorldBookmark.TimeStamp=2025.08.22-14.44.42
WorldBookmark.LastCompileMethod=Unknown
WorldPartitionEditor.TimeStamp=2025.08.22-14.44.43
WorldPartitionEditor.LastCompileMethod=Unknown
PacketHandler.TimeStamp=2025.08.22-14.44.24
PacketHandler.LastCompileMethod=Unknown
NetworkReplayStreaming.TimeStamp=2025.08.22-14.44.23
NetworkReplayStreaming.LastCompileMethod=Unknown
MassEntity.TimeStamp=2025.08.22-14.44.20
MassEntity.LastCompileMethod=Unknown
MassEntityTestSuite.TimeStamp=2025.08.22-14.44.20
MassEntityTestSuite.LastCompileMethod=Unknown
AndroidFileServer.TimeStamp=2025.08.22-14.55.38
AndroidFileServer.LastCompileMethod=Unknown
WebMMoviePlayer.TimeStamp=2025.08.22-14.56.25
WebMMoviePlayer.LastCompileMethod=Unknown
WindowsMoviePlayer.TimeStamp=2025.08.22-14.56.26
WindowsMoviePlayer.LastCompileMethod=Unknown
EnhancedInput.TimeStamp=2025.08.22-14.53.47
EnhancedInput.LastCompileMethod=Unknown
InputBlueprintNodes.TimeStamp=2025.08.22-14.53.47
InputBlueprintNodes.LastCompileMethod=Unknown
BlueprintGraph.TimeStamp=2025.08.22-14.43.56
BlueprintGraph.LastCompileMethod=Unknown
TcpMessaging.TimeStamp=2025.08.22-14.55.02
TcpMessaging.LastCompileMethod=Unknown
UdpMessaging.TimeStamp=2025.08.22-14.55.02
UdpMessaging.LastCompileMethod=Unknown
ActorSequence.TimeStamp=2025.08.22-14.55.12
ActorSequence.LastCompileMethod=Unknown
Paper2D.TimeStamp=2025.08.22-14.53.06
Paper2D.LastCompileMethod=Unknown
EnvironmentQueryEditor.TimeStamp=2025.08.22-14.53.07
EnvironmentQueryEditor.LastCompileMethod=Unknown
AnimationData.TimeStamp=2025.08.22-14.53.09
AnimationData.LastCompileMethod=Unknown
AnimationModifierLibrary.TimeStamp=2025.08.22-14.53.09
AnimationModifierLibrary.LastCompileMethod=Unknown
ControlRig.TimeStamp=2025.08.22-14.53.10
ControlRig.LastCompileMethod=Unknown
Constraints.TimeStamp=2025.08.22-14.43.59
Constraints.LastCompileMethod=Unknown
ControlRigDeveloper.TimeStamp=2025.08.22-14.53.10
ControlRigDeveloper.LastCompileMethod=Unknown
OptimusCore.TimeStamp=2025.08.22-14.53.15
OptimusCore.LastCompileMethod=Unknown
OptimusDeveloper.TimeStamp=2025.08.22-14.53.15
OptimusDeveloper.LastCompileMethod=Unknown
IKRig.TimeStamp=2025.08.22-14.53.19
IKRig.LastCompileMethod=Unknown
IKRigDeveloper.TimeStamp=2025.08.22-14.53.19
IKRigDeveloper.LastCompileMethod=Unknown
RigLogicLib.TimeStamp=2025.08.22-14.53.25
RigLogicLib.LastCompileMethod=Unknown
RigLogicLibTest.TimeStamp=2025.08.22-14.53.25
RigLogicLibTest.LastCompileMethod=Unknown
RigLogicDeveloper.TimeStamp=2025.08.22-14.53.25
RigLogicDeveloper.LastCompileMethod=Unknown
GameplayCameras.TimeStamp=2025.08.22-14.53.28
GameplayCameras.LastCompileMethod=Unknown
EngineCameras.TimeStamp=2025.08.22-14.53.28
EngineCameras.LastCompileMethod=Unknown
AnimationSharing.TimeStamp=2025.08.22-14.53.35
AnimationSharing.LastCompileMethod=Unknown
PropertyAccessNode.TimeStamp=2025.08.22-14.53.40
PropertyAccessNode.LastCompileMethod=Unknown
AssetManagerEditor.TimeStamp=2025.08.22-14.53.42
AssetManagerEditor.LastCompileMethod=Unknown
TreeMap.TimeStamp=2025.08.22-14.44.37
TreeMap.LastCompileMethod=Unknown
ContentBrowser.TimeStamp=2025.08.22-14.43.59
ContentBrowser.LastCompileMethod=Unknown
ContentBrowserData.TimeStamp=2025.08.22-14.43.59
ContentBrowserData.LastCompileMethod=Unknown
ToolMenus.TimeStamp=2025.08.22-14.44.36
ToolMenus.LastCompileMethod=Unknown
LevelEditor.TimeStamp=2025.08.22-14.44.19
LevelEditor.LastCompileMethod=Unknown
MainFrame.TimeStamp=2025.08.22-14.44.20
MainFrame.LastCompileMethod=Unknown
HotReload.TimeStamp=2025.08.22-14.44.15
HotReload.LastCompileMethod=Unknown
CommonMenuExtensions.TimeStamp=2025.08.22-14.43.59
CommonMenuExtensions.LastCompileMethod=Unknown
PixelInspectorModule.TimeStamp=2025.08.22-14.44.26
PixelInspectorModule.LastCompileMethod=Unknown
DataValidation.TimeStamp=2025.08.22-14.53.43
DataValidation.LastCompileMethod=Unknown
GameplayTagsEditor.TimeStamp=2025.08.22-14.53.44
GameplayTagsEditor.LastCompileMethod=Unknown
FacialAnimation.TimeStamp=2025.08.22-14.53.44
FacialAnimation.LastCompileMethod=Unknown
FacialAnimationEditor.TimeStamp=2025.08.22-14.53.44
FacialAnimationEditor.LastCompileMethod=Unknown
ChaosCaching.TimeStamp=2025.08.22-14.54.03
ChaosCaching.LastCompileMethod=Unknown
ChaosCachingEditor.TimeStamp=2025.08.22-14.54.03
ChaosCachingEditor.LastCompileMethod=Unknown
TakeRecorder.TimeStamp=2025.08.22-14.56.48
TakeRecorder.LastCompileMethod=Unknown
FullBodyIK.TimeStamp=2025.08.22-14.54.11
FullBodyIK.LastCompileMethod=Unknown
PBIK.TimeStamp=2025.08.22-14.54.11
PBIK.LastCompileMethod=Unknown
PythonScriptPlugin.TimeStamp=2025.08.22-14.54.28
PythonScriptPlugin.LastCompileMethod=Unknown
SequenceNavigator.TimeStamp=2025.08.22-14.54.31
SequenceNavigator.LastCompileMethod=Unknown
NiagaraSimCaching.TimeStamp=2025.08.22-14.54.46
NiagaraSimCaching.LastCompileMethod=Unknown
NiagaraSimCachingEditor.TimeStamp=2025.08.22-14.54.46
NiagaraSimCachingEditor.LastCompileMethod=Unknown
NiagaraCore.TimeStamp=2025.08.22-14.54.39
NiagaraCore.LastCompileMethod=Unknown
Niagara.TimeStamp=2025.08.22-14.54.39
Niagara.LastCompileMethod=Unknown
NiagaraEditor.TimeStamp=2025.08.22-14.54.41
NiagaraEditor.LastCompileMethod=Unknown
LevelSequence.TimeStamp=2025.08.22-14.44.19
LevelSequence.LastCompileMethod=Unknown
NiagaraAnimNotifies.TimeStamp=2025.08.22-14.54.39
NiagaraAnimNotifies.LastCompileMethod=Unknown
InterchangeNodes.TimeStamp=2025.08.22-14.54.48
InterchangeNodes.LastCompileMethod=Unknown
InterchangeFactoryNodes.TimeStamp=2025.08.22-14.54.48
InterchangeFactoryNodes.LastCompileMethod=Unknown
InterchangeImport.TimeStamp=2025.08.22-14.54.48
InterchangeImport.LastCompileMethod=Unknown
InterchangePipelines.TimeStamp=2025.08.22-14.54.49
InterchangePipelines.LastCompileMethod=Unknown
ImgMediaEngine.TimeStamp=2025.08.22-14.54.55
ImgMediaEngine.LastCompileMethod=Unknown
NNERuntimeORT.TimeStamp=2025.08.22-14.55.21
NNERuntimeORT.LastCompileMethod=Unknown
NNEEditor.TimeStamp=2025.08.22-14.44.24
NNEEditor.LastCompileMethod=Unknown
AudioSynesthesiaCore.TimeStamp=2025.08.22-14.55.39
AudioSynesthesiaCore.LastCompileMethod=Unknown
AudioSynesthesia.TimeStamp=2025.08.22-14.55.39
AudioSynesthesia.LastCompileMethod=Unknown
AudioAnalyzer.TimeStamp=2025.08.22-14.43.54
AudioAnalyzer.LastCompileMethod=Unknown
CableComponent.TimeStamp=2025.08.22-14.55.40
CableComponent.LastCompileMethod=Unknown
CustomMeshComponent.TimeStamp=2025.08.22-14.55.42
CustomMeshComponent.LastCompileMethod=Unknown
LocationServicesBPLibrary.TimeStamp=2025.08.22-14.55.56
LocationServicesBPLibrary.LastCompileMethod=Unknown
MetasoundGraphCore.TimeStamp=2025.08.22-14.56.02
MetasoundGraphCore.LastCompileMethod=Unknown
MetasoundGenerator.TimeStamp=2025.08.22-14.56.02
MetasoundGenerator.LastCompileMethod=Unknown
MetasoundFrontend.TimeStamp=2025.08.22-14.56.02
MetasoundFrontend.LastCompileMethod=Unknown
MetasoundStandardNodes.TimeStamp=2025.08.22-14.56.02
MetasoundStandardNodes.LastCompileMethod=Unknown
MetasoundEngine.TimeStamp=2025.08.22-14.56.01
MetasoundEngine.LastCompileMethod=Unknown
WaveTable.TimeStamp=2025.08.22-14.56.25
WaveTable.LastCompileMethod=Unknown
MetasoundEngineTest.TimeStamp=2025.08.22-14.56.01
MetasoundEngineTest.LastCompileMethod=Unknown
MetasoundEditor.TimeStamp=2025.08.22-14.56.01
MetasoundEditor.LastCompileMethod=Unknown
AudioWidgets.TimeStamp=2025.08.22-14.55.39
AudioWidgets.LastCompileMethod=Unknown
AdvancedWidgets.TimeStamp=2025.08.22-14.43.52
AdvancedWidgets.LastCompileMethod=Unknown
MsQuicRuntime.TimeStamp=2025.08.22-14.56.07
MsQuicRuntime.LastCompileMethod=Unknown
ProceduralMeshComponent.TimeStamp=2025.08.22-14.56.15
ProceduralMeshComponent.LastCompileMethod=Unknown
PropertyBindingUtils.TimeStamp=2025.08.22-14.56.15
PropertyBindingUtils.LastCompileMethod=Unknown
PropertyBindingUtilsTestSuite.TimeStamp=2025.08.22-14.56.15
PropertyBindingUtilsTestSuite.LastCompileMethod=Unknown
PropertyAccessEditor.TimeStamp=2025.08.22-14.56.15
PropertyAccessEditor.LastCompileMethod=Unknown
ResonanceAudio.TimeStamp=2025.08.22-14.56.16
ResonanceAudio.LastCompileMethod=Unknown
RigVM.TimeStamp=2025.08.22-14.56.17
RigVM.LastCompileMethod=Unknown
RigVMDeveloper.TimeStamp=2025.08.22-14.56.17
RigVMDeveloper.LastCompileMethod=Unknown
SignificanceManager.TimeStamp=2025.08.22-14.56.19
SignificanceManager.LastCompileMethod=Unknown
SoundFields.TimeStamp=2025.08.22-14.56.19
SoundFields.LastCompileMethod=Unknown
StateTreeModule.TimeStamp=2025.08.22-14.56.20
StateTreeModule.LastCompileMethod=Unknown
TraceServices.TimeStamp=2025.08.22-14.44.37
TraceServices.LastCompileMethod=Unknown
TraceAnalysis.TimeStamp=2025.08.22-14.44.36
TraceAnalysis.LastCompileMethod=Unknown
StateTreeTestSuite.TimeStamp=2025.08.22-14.56.20
StateTreeTestSuite.LastCompileMethod=Unknown
Synthesis.TimeStamp=2025.08.22-14.56.21
Synthesis.LastCompileMethod=Unknown
ChaosClothEditor.TimeStamp=2025.08.22-14.53.30
ChaosClothEditor.LastCompileMethod=Unknown
ChaosInsightsAnalysis.TimeStamp=2025.08.22-14.53.32
ChaosInsightsAnalysis.LastCompileMethod=Unknown
ChaosInsightsUI.TimeStamp=2025.08.22-14.53.32
ChaosInsightsUI.LastCompileMethod=Unknown
ChaosVD.TimeStamp=2025.08.22-14.53.33
ChaosVD.LastCompileMethod=Unknown
ChaosVDBlueprint.TimeStamp=2025.08.22-14.53.34
ChaosVDBlueprint.LastCompileMethod=Unknown
ChaosVDBuiltInExtensions.TimeStamp=2025.08.22-14.53.34
ChaosVDBuiltInExtensions.LastCompileMethod=Unknown
InputEditor.TimeStamp=2025.08.22-14.53.47
InputEditor.LastCompileMethod=Unknown
IoStoreInsights.TimeStamp=2025.08.22-14.54.51
IoStoreInsights.LastCompileMethod=Unknown
TraceInsights.TimeStamp=2025.08.22-14.44.37
TraceInsights.LastCompileMethod=Unknown
TraceInsightsCore.TimeStamp=2025.08.22-14.44.37
TraceInsightsCore.LastCompileMethod=Unknown
MassInsightsAnalysis.TimeStamp=2025.08.22-14.54.51
MassInsightsAnalysis.LastCompileMethod=Unknown
MassInsightsUI.TimeStamp=2025.08.22-14.54.51
MassInsightsUI.LastCompileMethod=Unknown
MeshPaintEditorMode.TimeStamp=2025.08.22-14.55.02
MeshPaintEditorMode.LastCompileMethod=Unknown
MeshPaintingToolset.TimeStamp=2025.08.22-14.55.02
MeshPaintingToolset.LastCompileMethod=Unknown
RenderGraphInsights.TimeStamp=2025.08.22-14.55.37
RenderGraphInsights.LastCompileMethod=Unknown
TraceUtilities.TimeStamp=2025.08.22-14.56.30
TraceUtilities.LastCompileMethod=Unknown
EditorTraceUtilities.TimeStamp=2025.08.22-14.56.30
EditorTraceUtilities.LastCompileMethod=Unknown
TraceTools.TimeStamp=2025.08.22-14.44.37
TraceTools.LastCompileMethod=Unknown
WorldMetricsCore.TimeStamp=2025.08.22-14.56.50
WorldMetricsCore.LastCompileMethod=Unknown
WorldMetricsTest.TimeStamp=2025.08.22-14.56.50
WorldMetricsTest.LastCompileMethod=Unknown
CsvMetrics.TimeStamp=2025.08.22-14.56.50
CsvMetrics.LastCompileMethod=Unknown
AlembicImporter.TimeStamp=2025.08.22-14.54.46
AlembicImporter.LastCompileMethod=Unknown
AlembicLibrary.TimeStamp=2025.08.22-14.54.46
AlembicLibrary.LastCompileMethod=Unknown
GeometryCache.TimeStamp=2025.08.22-14.55.45
GeometryCache.LastCompileMethod=Unknown
GeometryCacheEd.TimeStamp=2025.08.22-14.55.46
GeometryCacheEd.LastCompileMethod=Unknown
SequencerScripting.TimeStamp=2025.08.22-14.55.15
SequencerScripting.LastCompileMethod=Unknown
SequencerScriptingEditor.TimeStamp=2025.08.22-14.55.15
SequencerScriptingEditor.LastCompileMethod=Unknown
TemplateSequence.TimeStamp=2025.08.22-14.55.16
TemplateSequence.LastCompileMethod=Unknown
OnlineBase.TimeStamp=2025.08.22-14.55.21
OnlineBase.LastCompileMethod=Unknown
InterchangeTests.TimeStamp=2025.08.22-14.56.27
InterchangeTests.LastCompileMethod=Unknown
InterchangeTestEditor.TimeStamp=2025.08.22-14.56.27
InterchangeTestEditor.LastCompileMethod=Unknown
CameraCalibrationCoreEditor.TimeStamp=2025.08.22-14.56.36
CameraCalibrationCoreEditor.LastCompileMethod=Unknown
TakeMovieScene.TimeStamp=2025.08.22-14.56.48
TakeMovieScene.LastCompileMethod=Unknown
TakeSequencer.TimeStamp=2025.08.22-14.56.48
TakeSequencer.LastCompileMethod=Unknown
Paper2DEditor.TimeStamp=2025.08.22-14.53.07
Paper2DEditor.LastCompileMethod=Unknown
PaperSpriteSheetImporter.TimeStamp=2025.08.22-14.53.07
PaperSpriteSheetImporter.LastCompileMethod=Unknown
PaperTiledImporter.TimeStamp=2025.08.22-14.53.07
PaperTiledImporter.LastCompileMethod=Unknown
ACLPluginEditor.TimeStamp=2025.08.22-14.53.09
ACLPluginEditor.LastCompileMethod=Unknown
BlendSpaceMotionAnalysis.TimeStamp=2025.08.22-14.53.09
BlendSpaceMotionAnalysis.LastCompileMethod=Unknown
ControlRigSpline.TimeStamp=2025.08.22-14.53.15
ControlRigSpline.LastCompileMethod=Unknown
GameplayInsights.TimeStamp=2025.08.22-14.53.18
GameplayInsights.LastCompileMethod=Unknown
AnimationBlueprintEditor.TimeStamp=2025.08.22-14.43.53
AnimationBlueprintEditor.LastCompileMethod=Unknown
GameplayInsightsEditor.TimeStamp=2025.08.22-14.53.18
GameplayInsightsEditor.LastCompileMethod=Unknown
RewindDebuggerRuntime.TimeStamp=2025.08.22-14.53.18
RewindDebuggerRuntime.LastCompileMethod=Unknown
RewindDebuggerVLogRuntime.TimeStamp=2025.08.22-14.53.18
RewindDebuggerVLogRuntime.LastCompileMethod=Unknown
RigLogicModule.TimeStamp=2025.08.22-14.53.25
RigLogicModule.LastCompileMethod=Unknown
RigLogicEditor.TimeStamp=2025.08.22-14.53.25
RigLogicEditor.LastCompileMethod=Unknown
SkeletalMeshModelingTools.TimeStamp=2025.08.22-14.53.27
SkeletalMeshModelingTools.LastCompileMethod=Unknown
SkeletalMeshEditor.TimeStamp=2025.08.22-14.44.32
SkeletalMeshEditor.LastCompileMethod=Unknown
TweeningUtils.TimeStamp=2025.08.22-14.53.27
TweeningUtils.LastCompileMethod=Unknown
TweeningUtilsEditor.TimeStamp=2025.08.22-14.53.27
TweeningUtilsEditor.LastCompileMethod=Unknown
GameplayCamerasUncookedOnly.TimeStamp=2025.08.22-14.53.29
GameplayCamerasUncookedOnly.LastCompileMethod=Unknown
OodleNetworkHandlerComponent.TimeStamp=2025.08.22-14.53.35
OodleNetworkHandlerComponent.LastCompileMethod=Unknown
CLionSourceCodeAccess.TimeStamp=2025.08.22-14.53.35
CLionSourceCodeAccess.LastCompileMethod=Unknown
AnimationSharingEd.TimeStamp=2025.08.22-14.53.35
AnimationSharingEd.LastCompileMethod=Unknown
DumpGPUServices.TimeStamp=2025.08.22-14.53.39
DumpGPUServices.LastCompileMethod=Unknown
GitSourceControl.TimeStamp=2025.08.22-14.53.39
GitSourceControl.LastCompileMethod=Unknown
N10XSourceCodeAccess.TimeStamp=2025.08.22-14.53.39
N10XSourceCodeAccess.LastCompileMethod=Unknown
PluginUtils.TimeStamp=2025.08.22-14.53.40
PluginUtils.LastCompileMethod=Unknown
NamingTokens.TimeStamp=2025.08.22-14.53.39
NamingTokens.LastCompileMethod=Unknown
NamingTokensUncookedOnly.TimeStamp=2025.08.22-14.53.39
NamingTokensUncookedOnly.LastCompileMethod=Unknown
ProjectLauncher.TimeStamp=2025.08.22-14.53.40
ProjectLauncher.LastCompileMethod=Unknown
CommonLaunchExtensions.TimeStamp=2025.08.22-14.53.40
CommonLaunchExtensions.LastCompileMethod=Unknown
SubversionSourceControl.TimeStamp=2025.08.22-14.53.41
SubversionSourceControl.LastCompileMethod=Unknown
RiderSourceCodeAccess.TimeStamp=2025.08.22-14.53.41
RiderSourceCodeAccess.LastCompileMethod=Unknown
UObjectPlugin.TimeStamp=2025.08.22-14.53.42
UObjectPlugin.LastCompileMethod=Unknown
VisualStudioSourceCodeAccess.TimeStamp=2025.08.22-14.53.42
VisualStudioSourceCodeAccess.LastCompileMethod=Unknown
VisualStudioCodeSourceCodeAccess.TimeStamp=2025.08.22-14.53.42
VisualStudioCodeSourceCodeAccess.LastCompileMethod=Unknown
ChangelistReview.TimeStamp=2025.08.22-14.53.42
ChangelistReview.LastCompileMethod=Unknown
BlueprintHeaderView.TimeStamp=2025.08.22-14.53.42
BlueprintHeaderView.LastCompileMethod=Unknown
ColorGradingEditor.TimeStamp=2025.08.22-14.53.42
ColorGradingEditor.LastCompileMethod=Unknown
CurveEditorTools.TimeStamp=2025.08.22-14.53.43
CurveEditorTools.LastCompileMethod=Unknown
CryptoKeys.TimeStamp=2025.08.22-14.53.42
CryptoKeys.LastCompileMethod=Unknown
CryptoKeysOpenSSL.TimeStamp=2025.08.22-14.53.43
CryptoKeysOpenSSL.LastCompileMethod=Unknown
EditorScriptingUtilities.TimeStamp=2025.08.22-14.53.43
EditorScriptingUtilities.LastCompileMethod=Unknown
EditorDebugTools.TimeStamp=2025.08.22-14.53.43
EditorDebugTools.LastCompileMethod=Unknown
ModelingToolsEditorMode.TimeStamp=2025.08.22-14.53.45
ModelingToolsEditorMode.LastCompileMethod=Unknown
MaterialAnalyzer.TimeStamp=2025.08.22-14.53.45
MaterialAnalyzer.LastCompileMethod=Unknown
MeshLODToolset.TimeStamp=2025.08.22-14.53.45
MeshLODToolset.LastCompileMethod=Unknown
MobileLauncherProfileWizard.TimeStamp=2025.08.22-14.53.45
MobileLauncherProfileWizard.LastCompileMethod=Unknown
PluginBrowser.TimeStamp=2025.08.22-14.53.45
PluginBrowser.LastCompileMethod=Unknown
SequencerAnimTools.TimeStamp=2025.08.22-14.53.45
SequencerAnimTools.LastCompileMethod=Unknown
SpeedTreeImporter.TimeStamp=2025.08.22-14.53.45
SpeedTreeImporter.LastCompileMethod=Unknown
UVEditor.TimeStamp=2025.08.22-14.53.46
UVEditor.LastCompileMethod=Unknown
UVEditorTools.TimeStamp=2025.08.22-14.53.46
UVEditorTools.LastCompileMethod=Unknown
UVEditorToolsEditorOnly.TimeStamp=2025.08.22-14.53.47
UVEditorToolsEditorOnly.LastCompileMethod=Unknown
StylusInput.TimeStamp=2025.08.22-14.53.45
StylusInput.LastCompileMethod=Unknown
StylusInputDebugWidget.TimeStamp=2025.08.22-14.53.45
StylusInputDebugWidget.LastCompileMethod=Unknown
WorldPartitionHLODUtilities.TimeStamp=2025.08.22-14.53.47
WorldPartitionHLODUtilities.LastCompileMethod=Unknown
UMGWidgetPreview.TimeStamp=2025.08.22-14.53.45
UMGWidgetPreview.LastCompileMethod=Unknown
DatasmithContentEditor.TimeStamp=2025.08.22-14.53.52
DatasmithContentEditor.LastCompileMethod=Unknown
VariantManager.TimeStamp=2025.08.22-14.53.56
VariantManager.LastCompileMethod=Unknown
VariantManagerContentEditor.TimeStamp=2025.08.22-14.53.56
VariantManagerContentEditor.LastCompileMethod=Unknown
AdvancedRenamer.TimeStamp=2025.08.22-14.53.56
AdvancedRenamer.LastCompileMethod=Unknown
AutomationUtils.TimeStamp=2025.08.22-14.54.00
AutomationUtils.LastCompileMethod=Unknown
AutomationUtilsEditor.TimeStamp=2025.08.22-14.54.00
AutomationUtilsEditor.LastCompileMethod=Unknown
BackChannel.TimeStamp=2025.08.22-14.54.01
BackChannel.LastCompileMethod=Unknown
FractureEditor.TimeStamp=2025.08.22-14.54.03
FractureEditor.LastCompileMethod=Unknown
ChaosNiagara.TimeStamp=2025.08.22-14.54.05
ChaosNiagara.LastCompileMethod=Unknown
ChaosUserDataPT.TimeStamp=2025.08.22-14.54.05
ChaosUserDataPT.LastCompileMethod=Unknown
ChaosSolverEditor.TimeStamp=2025.08.22-14.54.05
ChaosSolverEditor.LastCompileMethod=Unknown
DataflowAssetTools.TimeStamp=2025.08.22-14.54.08
DataflowAssetTools.LastCompileMethod=Unknown
DataflowEnginePlugin.TimeStamp=2025.08.22-14.54.08
DataflowEnginePlugin.LastCompileMethod=Unknown
DataflowEngine.TimeStamp=2025.08.22-14.44.03
DataflowEngine.LastCompileMethod=Unknown
DataflowSimulation.TimeStamp=2025.08.22-14.44.03
DataflowSimulation.LastCompileMethod=Unknown
DataflowNodes.TimeStamp=2025.08.22-14.54.08
DataflowNodes.LastCompileMethod=Unknown
TedsCore.TimeStamp=2025.08.22-14.54.10
TedsCore.LastCompileMethod=Unknown
TypedElementFramework.TimeStamp=2025.08.22-14.44.37
TypedElementFramework.LastCompileMethod=Unknown
MassEntityEditor.TimeStamp=2025.08.22-14.44.20
MassEntityEditor.LastCompileMethod=Unknown
MassEntityDebugger.TimeStamp=2025.08.22-14.44.20
MassEntityDebugger.LastCompileMethod=Unknown
TedsUI.TimeStamp=2025.08.22-14.54.10
TedsUI.LastCompileMethod=Unknown
TedsAlerts.TimeStamp=2025.08.22-14.54.10
TedsAlerts.LastCompileMethod=Unknown
TedsAssetData.TimeStamp=2025.08.22-14.54.10
TedsAssetData.LastCompileMethod=Unknown
TedsContentBrowser.TimeStamp=2025.08.22-14.54.10
TedsContentBrowser.LastCompileMethod=Unknown
TedsDebugger.TimeStamp=2025.08.22-14.54.10
TedsDebugger.LastCompileMethod=Unknown
TedsOutliner.TimeStamp=2025.08.22-14.54.10
TedsOutliner.LastCompileMethod=Unknown
TedsPropertyEditor.TimeStamp=2025.08.22-14.54.10
TedsPropertyEditor.LastCompileMethod=Unknown
TedsQueryStack.TimeStamp=2025.08.22-14.54.10
TedsQueryStack.LastCompileMethod=Unknown
TedsRevisionControl.TimeStamp=2025.08.22-14.54.10
TedsRevisionControl.LastCompileMethod=Unknown
TedsSettings.TimeStamp=2025.08.22-14.54.10
TedsSettings.LastCompileMethod=Unknown
TedsTableViewer.TimeStamp=2025.08.22-14.54.10
TedsTableViewer.LastCompileMethod=Unknown
GeometryFlowCore.TimeStamp=2025.08.22-14.54.14
GeometryFlowCore.LastCompileMethod=Unknown
GeometryFlowMeshProcessing.TimeStamp=2025.08.22-14.54.14
GeometryFlowMeshProcessing.LastCompileMethod=Unknown
GeometryFlowMeshProcessingEditor.TimeStamp=2025.08.22-14.54.14
GeometryFlowMeshProcessingEditor.LastCompileMethod=Unknown
GeometryDataflowNodes.TimeStamp=2025.08.22-14.54.14
GeometryDataflowNodes.LastCompileMethod=Unknown
GeometryCollectionEditor.TimeStamp=2025.08.22-14.54.12
GeometryCollectionEditor.LastCompileMethod=Unknown
GeometryCollectionTracks.TimeStamp=2025.08.22-14.54.12
GeometryCollectionTracks.LastCompileMethod=Unknown
GeometryCollectionSequencer.TimeStamp=2025.08.22-14.54.12
GeometryCollectionSequencer.LastCompileMethod=Unknown
GeometryCollectionEngine.TimeStamp=2025.08.22-14.44.14
GeometryCollectionEngine.LastCompileMethod=Unknown
GeometryCollectionNodes.TimeStamp=2025.08.22-14.54.12
GeometryCollectionNodes.LastCompileMethod=Unknown
GeometryCollectionDepNodes.TimeStamp=2025.08.22-14.54.12
GeometryCollectionDepNodes.LastCompileMethod=Unknown
MeshModelingToolsExp.TimeStamp=2025.08.22-14.54.18
MeshModelingToolsExp.LastCompileMethod=Unknown
MeshModelingToolsEditorOnlyExp.TimeStamp=2025.08.22-14.54.18
MeshModelingToolsEditorOnlyExp.LastCompileMethod=Unknown
GeometryProcessingAdapters.TimeStamp=2025.08.22-14.54.18
GeometryProcessingAdapters.LastCompileMethod=Unknown
ModelingEditorUI.TimeStamp=2025.08.22-14.54.18
ModelingEditorUI.LastCompileMethod=Unknown
ModelingUI.TimeStamp=2025.08.22-14.54.18
ModelingUI.LastCompileMethod=Unknown
LocalizableMessage.TimeStamp=2025.08.22-14.54.17
LocalizableMessage.LastCompileMethod=Unknown
LocalizableMessageBlueprint.TimeStamp=2025.08.22-14.54.17
LocalizableMessageBlueprint.LastCompileMethod=Unknown
ToolPresetAsset.TimeStamp=2025.08.22-14.54.34
ToolPresetAsset.LastCompileMethod=Unknown
ToolPresetEditor.TimeStamp=2025.08.22-14.54.34
ToolPresetEditor.LastCompileMethod=Unknown
Cascade.TimeStamp=2025.08.22-14.54.38
Cascade.LastCompileMethod=Unknown
InterchangeEditor.TimeStamp=2025.08.22-14.54.47
InterchangeEditor.LastCompileMethod=Unknown
InterchangeEditorPipelines.TimeStamp=2025.08.22-14.54.48
InterchangeEditorPipelines.LastCompileMethod=Unknown
InterchangeEditorUtilities.TimeStamp=2025.08.22-14.54.48
InterchangeEditorUtilities.LastCompileMethod=Unknown
NiagaraBlueprintNodes.TimeStamp=2025.08.22-14.54.39
NiagaraBlueprintNodes.LastCompileMethod=Unknown
NiagaraEditorWidgets.TimeStamp=2025.08.22-14.54.41
NiagaraEditorWidgets.LastCompileMethod=Unknown
GLTFCore.TimeStamp=2025.08.22-14.54.48
GLTFCore.LastCompileMethod=Unknown
InterchangeMessages.TimeStamp=2025.08.22-14.54.48
InterchangeMessages.LastCompileMethod=Unknown
InterchangeExport.TimeStamp=2025.08.22-14.54.48
InterchangeExport.LastCompileMethod=Unknown
InterchangeDispatcher.TimeStamp=2025.08.22-14.54.48
InterchangeDispatcher.LastCompileMethod=Unknown
InterchangeCommon.TimeStamp=2025.08.22-14.54.48
InterchangeCommon.LastCompileMethod=Unknown
InterchangeCommonParser.TimeStamp=2025.08.22-14.54.48
InterchangeCommonParser.LastCompileMethod=Unknown
InterchangeFbxParser.TimeStamp=2025.08.22-14.54.48
InterchangeFbxParser.LastCompileMethod=Unknown
ImgMedia.TimeStamp=2025.08.22-14.54.55
ImgMedia.LastCompileMethod=Unknown
MediaCompositing.TimeStamp=2025.08.22-14.54.56
MediaCompositing.LastCompileMethod=Unknown
MediaPlate.TimeStamp=2025.08.22-14.54.57
MediaPlate.LastCompileMethod=Unknown
MediaPlateEditor.TimeStamp=2025.08.22-14.54.57
MediaPlateEditor.LastCompileMethod=Unknown
MetaHumanSDKEditor.TimeStamp=2025.08.22-14.55.12
MetaHumanSDKEditor.LastCompileMethod=Unknown
MetaHumanSDKRuntime.TimeStamp=2025.08.22-14.55.12
MetaHumanSDKRuntime.LastCompileMethod=Unknown
NNEDenoiser.TimeStamp=2025.08.22-14.55.20
NNEDenoiser.LastCompileMethod=Unknown
ActorLayerUtilities.TimeStamp=2025.08.22-14.55.37
ActorLayerUtilities.LastCompileMethod=Unknown
ActorLayerUtilitiesEditor.TimeStamp=2025.08.22-14.55.37
ActorLayerUtilitiesEditor.LastCompileMethod=Unknown
AndroidPermission.TimeStamp=2025.08.22-14.55.38
AndroidPermission.LastCompileMethod=Unknown
AssetTags.TimeStamp=2025.08.22-14.55.39
AssetTags.LastCompileMethod=Unknown
AppleImageUtils.TimeStamp=2025.08.22-14.55.38
AppleImageUtils.LastCompileMethod=Unknown
AppleImageUtilsBlueprintSupport.TimeStamp=2025.08.22-14.55.38
AppleImageUtilsBlueprintSupport.LastCompileMethod=Unknown
AudioCapture.TimeStamp=2025.08.22-14.55.39
AudioCapture.LastCompileMethod=Unknown
AudioCaptureWasapi.TimeStamp=2025.08.22-14.43.54
AudioCaptureWasapi.LastCompileMethod=Unknown
ArchVisCharacter.TimeStamp=2025.08.22-14.55.39
ArchVisCharacter.LastCompileMethod=Unknown
ComputeFrameworkEditor.TimeStamp=2025.08.22-14.55.41
ComputeFrameworkEditor.LastCompileMethod=Unknown
AudioWidgetsEditor.TimeStamp=2025.08.22-14.55.40
AudioWidgetsEditor.LastCompileMethod=Unknown
GeometryAlgorithms.TimeStamp=2025.08.22-14.55.46
GeometryAlgorithms.LastCompileMethod=Unknown
DynamicMesh.TimeStamp=2025.08.22-14.55.46
DynamicMesh.LastCompileMethod=Unknown
MeshFileUtils.TimeStamp=2025.08.22-14.55.46
MeshFileUtils.LastCompileMethod=Unknown
GeometryCacheSequencer.TimeStamp=2025.08.22-14.55.46
GeometryCacheSequencer.LastCompileMethod=Unknown
GeometryCacheStreamer.TimeStamp=2025.08.22-14.55.46
GeometryCacheStreamer.LastCompileMethod=Unknown
GeometryCacheTracks.TimeStamp=2025.08.22-14.55.46
GeometryCacheTracks.LastCompileMethod=Unknown
HairStrandsDeformer.TimeStamp=2025.08.22-14.55.50
HairStrandsDeformer.LastCompileMethod=Unknown
HairStrandsRuntime.TimeStamp=2025.08.22-14.55.50
HairStrandsRuntime.LastCompileMethod=Unknown
HairStrandsEditor.TimeStamp=2025.08.22-14.55.50
HairStrandsEditor.LastCompileMethod=Unknown
HairCardGeneratorFramework.TimeStamp=2025.08.22-14.55.50
HairCardGeneratorFramework.LastCompileMethod=Unknown
HairStrandsDataflow.TimeStamp=2025.08.22-14.55.50
HairStrandsDataflow.LastCompileMethod=Unknown
HairStrandsSolver.TimeStamp=2025.08.22-14.55.50
HairStrandsSolver.LastCompileMethod=Unknown
GooglePAD.TimeStamp=2025.08.22-14.55.50
GooglePAD.LastCompileMethod=Unknown
InputDebugging.TimeStamp=2025.08.22-14.55.56
InputDebugging.LastCompileMethod=Unknown
InputDebuggingEditor.TimeStamp=2025.08.22-14.55.56
InputDebuggingEditor.LastCompileMethod=Unknown
MobilePatchingUtils.TimeStamp=2025.08.22-14.56.07
MobilePatchingUtils.LastCompileMethod=Unknown
MeshModelingTools.TimeStamp=2025.08.22-14.55.57
MeshModelingTools.LastCompileMethod=Unknown
MeshModelingToolsEditorOnly.TimeStamp=2025.08.22-14.55.57
MeshModelingToolsEditorOnly.LastCompileMethod=Unknown
ModelingComponents.TimeStamp=2025.08.22-14.55.57
ModelingComponents.LastCompileMethod=Unknown
GeometryFramework.TimeStamp=2025.08.22-14.44.14
GeometryFramework.LastCompileMethod=Unknown
ModelingComponentsEditorOnly.TimeStamp=2025.08.22-14.55.57
ModelingComponentsEditorOnly.LastCompileMethod=Unknown
ModelingOperators.TimeStamp=2025.08.22-14.55.57
ModelingOperators.LastCompileMethod=Unknown
ModelingOperatorsEditorOnly.TimeStamp=2025.08.22-14.55.57
ModelingOperatorsEditorOnly.LastCompileMethod=Unknown
SkeletalMeshModifiers.TimeStamp=2025.08.22-14.55.57
SkeletalMeshModifiers.LastCompileMethod=Unknown
ProceduralMeshComponentEditor.TimeStamp=2025.08.22-14.56.15
ProceduralMeshComponentEditor.LastCompileMethod=Unknown
PropertyBindingUtilsEditor.TimeStamp=2025.08.22-14.56.15
PropertyBindingUtilsEditor.LastCompileMethod=Unknown
StateTreeEditorModule.TimeStamp=2025.08.22-14.56.20
StateTreeEditorModule.LastCompileMethod=Unknown
SynthesisEditor.TimeStamp=2025.08.22-14.56.21
SynthesisEditor.LastCompileMethod=Unknown
ContentBrowserAssetDataSource.TimeStamp=2025.08.22-14.53.42
ContentBrowserAssetDataSource.LastCompileMethod=Unknown
CollectionManager.TimeStamp=2025.08.22-14.43.59
CollectionManager.LastCompileMethod=Unknown
ContentBrowserFileDataSource.TimeStamp=2025.08.22-14.53.42
ContentBrowserFileDataSource.LastCompileMethod=Unknown
BaseCharacterFXEditor.TimeStamp=2025.08.22-14.54.06
BaseCharacterFXEditor.LastCompileMethod=Unknown
ContentBrowserClassDataSource.TimeStamp=2025.08.22-14.53.42
ContentBrowserClassDataSource.LastCompileMethod=Unknown
LightMixer.TimeStamp=2025.08.22-14.53.45
LightMixer.LastCompileMethod=Unknown
ObjectMixerEditor.TimeStamp=2025.08.22-14.53.45
ObjectMixerEditor.LastCompileMethod=Unknown
XInputDevice.TimeStamp=2025.08.22-14.56.26
XInputDevice.LastCompileMethod=Unknown
PortableObjectFileDataSource.TimeStamp=2025.08.22-14.53.45
PortableObjectFileDataSource.LastCompileMethod=Unknown
CmdLinkServer.TimeStamp=2025.08.22-14.53.34
CmdLinkServer.LastCompileMethod=Unknown
TakesCore.TimeStamp=2025.08.22-14.56.48
TakesCore.LastCompileMethod=Unknown
TakeTrackRecorders.TimeStamp=2025.08.22-14.56.48
TakeTrackRecorders.LastCompileMethod=Unknown
TakeRecorderSources.TimeStamp=2025.08.22-14.56.48
TakeRecorderSources.LastCompileMethod=Unknown
CacheTrackRecorder.TimeStamp=2025.08.22-14.56.48
CacheTrackRecorder.LastCompileMethod=Unknown
TakeRecorderNamingTokens.TimeStamp=2025.08.22-14.56.48
TakeRecorderNamingTokens.LastCompileMethod=Unknown
DataflowEditor.TimeStamp=2025.08.22-14.54.08
DataflowEditor.LastCompileMethod=Unknown
LevelSequenceNavigatorBridge.TimeStamp=2025.08.22-14.54.17
LevelSequenceNavigatorBridge.LastCompileMethod=Unknown
AudioSynesthesiaEditor.TimeStamp=2025.08.22-14.55.39
AudioSynesthesiaEditor.LastCompileMethod=Unknown
ProfileVisualizer.TimeStamp=2025.08.22-14.44.26
ProfileVisualizer.LastCompileMethod=Unknown
ImageWriteQueue.TimeStamp=2025.08.22-14.44.15
ImageWriteQueue.LastCompileMethod=Unknown
TypedElementRuntime.TimeStamp=2025.08.22-14.44.37
TypedElementRuntime.LastCompileMethod=Unknown
LevelInstanceEditor.TimeStamp=2025.08.22-14.44.19
LevelInstanceEditor.LastCompileMethod=Unknown
ChaosVDRuntime.TimeStamp=2025.08.22-14.43.58
ChaosVDRuntime.LastCompileMethod=Unknown
AIModule.TimeStamp=2025.08.22-14.43.53
AIModule.LastCompileMethod=Unknown
NavigationSystem.TimeStamp=2025.08.22-14.44.23
NavigationSystem.LastCompileMethod=Unknown
AITestSuite.TimeStamp=2025.08.22-14.43.53
AITestSuite.LastCompileMethod=Unknown
GameplayDebugger.TimeStamp=2025.08.22-14.44.13
GameplayDebugger.LastCompileMethod=Unknown
MessagingRpc.TimeStamp=2025.08.22-14.44.22
MessagingRpc.LastCompileMethod=Unknown
PortalRpc.TimeStamp=2025.08.22-14.44.26
PortalRpc.LastCompileMethod=Unknown
PortalServices.TimeStamp=2025.08.22-14.44.26
PortalServices.LastCompileMethod=Unknown
AnalyticsET.TimeStamp=2025.08.22-14.43.53
AnalyticsET.LastCompileMethod=Unknown
LauncherPlatform.TimeStamp=2025.08.22-14.44.18
LauncherPlatform.LastCompileMethod=Unknown
AudioMixerXAudio2.TimeStamp=2025.08.22-14.43.55
AudioMixerXAudio2.LastCompileMethod=Unknown
AudioMixer.TimeStamp=2025.08.22-14.43.55
AudioMixer.LastCompileMethod=Unknown
StreamingPauseRendering.TimeStamp=2025.08.22-14.44.35
StreamingPauseRendering.LastCompileMethod=Unknown
MovieScene.TimeStamp=2025.08.22-14.44.22
MovieScene.LastCompileMethod=Unknown
MovieSceneTracks.TimeStamp=2025.08.22-14.44.23
MovieSceneTracks.LastCompileMethod=Unknown
CinematicCamera.TimeStamp=2025.08.22-14.43.58
CinematicCamera.LastCompileMethod=Unknown
SparseVolumeTexture.TimeStamp=2025.08.22-14.44.34
SparseVolumeTexture.LastCompileMethod=Unknown
Documentation.TimeStamp=2025.08.22-14.44.06
Documentation.LastCompileMethod=Unknown
OutputLog.TimeStamp=2025.08.22-14.44.24
OutputLog.LastCompileMethod=Unknown
SourceControlWindows.TimeStamp=2025.08.22-14.44.34
SourceControlWindows.LastCompileMethod=Unknown
SourceControlWindowExtender.TimeStamp=2025.08.22-14.44.34
SourceControlWindowExtender.LastCompileMethod=Unknown
UncontrolledChangelists.TimeStamp=2025.08.22-14.44.38
UncontrolledChangelists.LastCompileMethod=Unknown
ClassViewer.TimeStamp=2025.08.22-14.43.59
ClassViewer.LastCompileMethod=Unknown
StructViewer.TimeStamp=2025.08.22-14.44.35
StructViewer.LastCompileMethod=Unknown
GraphEditor.TimeStamp=2025.08.22-14.44.14
GraphEditor.LastCompileMethod=Unknown
Kismet.TimeStamp=2025.08.22-14.44.18
Kismet.LastCompileMethod=Unknown
KismetWidgets.TimeStamp=2025.08.22-14.44.18
KismetWidgets.LastCompileMethod=Unknown
Persona.TimeStamp=2025.08.22-14.44.25
Persona.LastCompileMethod=Unknown
AdvancedPreviewScene.TimeStamp=2025.08.22-14.43.52
AdvancedPreviewScene.LastCompileMethod=Unknown
PackagesDialog.TimeStamp=2025.08.22-14.44.24
PackagesDialog.LastCompileMethod=Unknown
DetailCustomizations.TimeStamp=2025.08.22-14.44.05
DetailCustomizations.LastCompileMethod=Unknown
ComponentVisualizers.TimeStamp=2025.08.22-14.43.59
ComponentVisualizers.LastCompileMethod=Unknown
Layers.TimeStamp=2025.08.22-14.44.18
Layers.LastCompileMethod=Unknown
AutomationWindow.TimeStamp=2025.08.22-14.43.55
AutomationWindow.LastCompileMethod=Unknown
AutomationController.TimeStamp=2025.08.22-14.43.55
AutomationController.LastCompileMethod=Unknown
DeviceManager.TimeStamp=2025.08.22-14.44.05
DeviceManager.LastCompileMethod=Unknown
ProfilerClient.TimeStamp=
ProfilerClient.LastCompileMethod=Unknown
SessionFrontend.TimeStamp=2025.08.22-14.44.31
SessionFrontend.LastCompileMethod=Unknown
LegacyProjectLauncher.TimeStamp=2025.08.22-14.44.18
LegacyProjectLauncher.LastCompileMethod=Unknown
SettingsEditor.TimeStamp=2025.08.22-14.44.32
SettingsEditor.LastCompileMethod=Unknown
EditorSettingsViewer.TimeStamp=2025.08.22-14.44.06
EditorSettingsViewer.LastCompileMethod=Unknown
InternationalizationSettings.TimeStamp=2025.08.22-14.44.16
InternationalizationSettings.LastCompileMethod=Unknown
ProjectSettingsViewer.TimeStamp=2025.08.22-14.44.26
ProjectSettingsViewer.LastCompileMethod=Unknown
ProjectTargetPlatformEditor.TimeStamp=2025.08.22-14.44.26
ProjectTargetPlatformEditor.LastCompileMethod=Unknown
Blutility.TimeStamp=2025.08.22-14.43.56
Blutility.LastCompileMethod=Unknown
XmlParser.TimeStamp=2025.08.22-14.44.43
XmlParser.LastCompileMethod=Unknown
UndoHistory.TimeStamp=2025.08.22-14.44.38
UndoHistory.LastCompileMethod=Unknown
DeviceProfileEditor.TimeStamp=2025.08.22-14.44.06
DeviceProfileEditor.LastCompileMethod=Unknown
HardwareTargeting.TimeStamp=2025.08.22-14.44.14
HardwareTargeting.LastCompileMethod=Unknown
LocalizationDashboard.TimeStamp=2025.08.22-14.44.20
LocalizationDashboard.LastCompileMethod=Unknown
LocalizationService.TimeStamp=2025.08.22-14.44.20
LocalizationService.LastCompileMethod=Unknown
MergeActors.TimeStamp=2025.08.22-14.44.21
MergeActors.LastCompileMethod=Unknown
InputBindingEditor.TimeStamp=2025.08.22-14.44.15
InputBindingEditor.LastCompileMethod=Unknown
EditorInteractiveToolsFramework.TimeStamp=2025.08.22-14.44.06
EditorInteractiveToolsFramework.LastCompileMethod=Unknown
InteractiveToolsFramework.TimeStamp=2025.08.22-14.44.15
InteractiveToolsFramework.LastCompileMethod=Unknown
StaticMeshEditor.TimeStamp=2025.08.22-14.44.35
StaticMeshEditor.LastCompileMethod=Unknown
EditorFramework.TimeStamp=2025.08.22-14.44.06
EditorFramework.LastCompileMethod=Unknown
EditorConfig.TimeStamp=2025.08.22-14.44.06
EditorConfig.LastCompileMethod=Unknown
DerivedDataEditor.TimeStamp=2025.08.22-14.44.04
DerivedDataEditor.LastCompileMethod=Unknown
ZenEditor.TimeStamp=2025.08.22-14.44.43
ZenEditor.LastCompileMethod=Unknown
CSVtoSVG.TimeStamp=2025.08.22-14.44.02
CSVtoSVG.LastCompileMethod=Unknown
VirtualizationEditor.TimeStamp=2025.08.22-14.44.41
VirtualizationEditor.LastCompileMethod=Unknown
AnimationSettings.TimeStamp=2025.08.22-14.43.53
AnimationSettings.LastCompileMethod=Unknown
GameplayDebuggerEditor.TimeStamp=2025.08.22-14.44.13
GameplayDebuggerEditor.LastCompileMethod=Unknown
RenderResourceViewer.TimeStamp=2025.08.22-14.44.30
RenderResourceViewer.LastCompileMethod=Unknown
UniversalObjectLocatorEditor.TimeStamp=2025.08.22-14.44.38
UniversalObjectLocatorEditor.LastCompileMethod=Unknown
StructUtilsEditor.TimeStamp=2025.08.22-14.44.35
StructUtilsEditor.LastCompileMethod=Unknown
StructUtilsTestSuite.TimeStamp=2025.08.22-14.44.35
StructUtilsTestSuite.LastCompileMethod=Unknown
SVGDistanceField.TimeStamp=2025.08.22-14.44.35
SVGDistanceField.LastCompileMethod=Unknown
DataHierarchyEditor.TimeStamp=2025.08.22-14.44.03
DataHierarchyEditor.LastCompileMethod=Unknown
AndroidRuntimeSettings.TimeStamp=2025.08.22-14.43.23
AndroidRuntimeSettings.LastCompileMethod=Unknown
IOSRuntimeSettings.TimeStamp=2025.08.22-14.43.34
IOSRuntimeSettings.LastCompileMethod=Unknown
MacPlatformEditor.TimeStamp=2025.08.22-14.44.20
MacPlatformEditor.LastCompileMethod=Unknown
WindowsPlatformEditor.TimeStamp=2025.08.22-14.44.42
WindowsPlatformEditor.LastCompileMethod=Unknown
AndroidPlatformEditor.TimeStamp=2025.08.22-14.43.23
AndroidPlatformEditor.LastCompileMethod=Unknown
AndroidDeviceDetection.TimeStamp=2025.08.22-14.43.23
AndroidDeviceDetection.LastCompileMethod=Unknown
PIEPreviewDeviceProfileSelector.TimeStamp=2025.08.22-14.44.25
PIEPreviewDeviceProfileSelector.LastCompileMethod=Unknown
IOSPlatformEditor.TimeStamp=2025.08.22-14.43.34
IOSPlatformEditor.LastCompileMethod=Unknown
LogVisualizer.TimeStamp=2025.08.22-14.44.20
LogVisualizer.LastCompileMethod=Unknown
WidgetRegistration.TimeStamp=2025.08.22-14.44.42
WidgetRegistration.LastCompileMethod=Unknown
ClothPainter.TimeStamp=2025.08.22-14.43.59
ClothPainter.LastCompileMethod=Unknown
ViewportInteraction.TimeStamp=2025.08.22-14.44.41
ViewportInteraction.LastCompileMethod=Unknown
EditorWidgets.TimeStamp=2025.08.22-14.44.06
EditorWidgets.LastCompileMethod=Unknown
ViewportSnapping.TimeStamp=2025.08.22-14.44.41
ViewportSnapping.LastCompileMethod=Unknown
PlacementMode.TimeStamp=2025.08.22-14.44.26
PlacementMode.LastCompileMethod=Unknown
MeshPaint.TimeStamp=2025.08.22-14.44.21
MeshPaint.LastCompileMethod=Unknown
SessionServices.TimeStamp=2025.08.22-14.44.32
SessionServices.LastCompileMethod=Unknown
ActorSequenceEditor.TimeStamp=2025.08.22-14.55.12
ActorSequenceEditor.LastCompileMethod=Unknown
LevelSequenceEditor.TimeStamp=2025.08.22-14.55.12
LevelSequenceEditor.LastCompileMethod=Unknown
TemplateSequenceEditor.TimeStamp=2025.08.22-14.55.16
TemplateSequenceEditor.LastCompileMethod=Unknown
SmartSnapping.TimeStamp=2025.08.22-14.53.07
SmartSnapping.LastCompileMethod=Unknown
ControlRigEditor.TimeStamp=2025.08.22-14.53.11
ControlRigEditor.LastCompileMethod=Unknown
OptimusEditor.TimeStamp=2025.08.22-14.53.15
OptimusEditor.LastCompileMethod=Unknown
RewindDebugger.TimeStamp=2025.08.22-14.53.18
RewindDebugger.LastCompileMethod=Unknown
RewindDebuggerVLog.TimeStamp=2025.08.22-14.53.18
RewindDebuggerVLog.LastCompileMethod=Unknown
IKRigEditor.TimeStamp=2025.08.22-14.53.19
IKRigEditor.LastCompileMethod=Unknown
CameraShakePreviewer.TimeStamp=2025.08.22-14.53.28
CameraShakePreviewer.LastCompileMethod=Unknown
GameplayCamerasEditor.TimeStamp=2025.08.22-14.53.29
GameplayCamerasEditor.LastCompileMethod=Unknown
EngineAssetDefinitions.TimeStamp=2025.08.22-14.53.44
EngineAssetDefinitions.LastCompileMethod=Unknown
GeometryMode.TimeStamp=2025.08.22-14.53.44
GeometryMode.LastCompileMethod=Unknown
BspMode.TimeStamp=2025.08.22-14.53.44
BspMode.LastCompileMethod=Unknown
TextureAlignMode.TimeStamp=2025.08.22-14.53.44
TextureAlignMode.LastCompileMethod=Unknown
CharacterAI.TimeStamp=2025.08.22-14.54.06
CharacterAI.LastCompileMethod=Unknown
FractureEngine.TimeStamp=2025.08.22-14.54.11
FractureEngine.LastCompileMethod=Unknown
PlanarCut.TimeStamp=2025.08.22-14.54.27
PlanarCut.LastCompileMethod=Unknown
AndroidMediaEditor.TimeStamp=2025.08.22-14.54.51
AndroidMediaEditor.LastCompileMethod=Unknown
AndroidMediaFactory.TimeStamp=2025.08.22-14.54.51
AndroidMediaFactory.LastCompileMethod=Unknown
AvfMediaEditor.TimeStamp=2025.08.22-14.54.52
AvfMediaEditor.LastCompileMethod=Unknown
AvfMediaFactory.TimeStamp=2025.08.22-14.54.52
AvfMediaFactory.LastCompileMethod=Unknown
ImgMediaEditor.TimeStamp=2025.08.22-14.54.55
ImgMediaEditor.LastCompileMethod=Unknown
ImgMediaFactory.TimeStamp=2025.08.22-14.54.55
ImgMediaFactory.LastCompileMethod=Unknown
OpenExrWrapper.TimeStamp=2025.08.22-14.54.56
OpenExrWrapper.LastCompileMethod=Unknown
MediaPlayerEditor.TimeStamp=2025.08.22-14.54.57
MediaPlayerEditor.LastCompileMethod=Unknown
MediaCompositingEditor.TimeStamp=2025.08.22-14.54.56
MediaCompositingEditor.LastCompileMethod=Unknown
SequenceRecorder.TimeStamp=2025.08.22-14.44.31
SequenceRecorder.LastCompileMethod=Unknown
WmfMediaEditor.TimeStamp=2025.08.22-14.55.02
WmfMediaEditor.LastCompileMethod=Unknown
WmfMediaFactory.TimeStamp=2025.08.22-14.55.02
WmfMediaFactory.LastCompileMethod=Unknown
WebMMedia.TimeStamp=2025.08.22-14.55.02
WebMMedia.LastCompileMethod=Unknown
WebMMediaEditor.TimeStamp=2025.08.22-14.55.02
WebMMediaEditor.LastCompileMethod=Unknown
WebMMediaFactory.TimeStamp=2025.08.22-14.55.02
WebMMediaFactory.LastCompileMethod=Unknown
AndroidFileServerEditor.TimeStamp=2025.08.22-14.55.38
AndroidFileServerEditor.LastCompileMethod=Unknown
AudioCaptureEditor.TimeStamp=2025.08.22-14.55.39
AudioCaptureEditor.LastCompileMethod=Unknown
GooglePADEditor.TimeStamp=2025.08.22-14.55.50
GooglePADEditor.LastCompileMethod=Unknown
ResonanceAudioEditor.TimeStamp=2025.08.22-14.56.16
ResonanceAudioEditor.LastCompileMethod=Unknown
RigVMEditor.TimeStamp=2025.08.22-14.56.17
RigVMEditor.LastCompileMethod=Unknown
WaveTableEditor.TimeStamp=2025.08.22-14.56.25
WaveTableEditor.LastCompileMethod=Unknown
ActorPickerMode.TimeStamp=2025.08.22-14.43.52
ActorPickerMode.LastCompileMethod=Unknown
SceneDepthPickerMode.TimeStamp=2025.08.22-14.44.30
SceneDepthPickerMode.LastCompileMethod=Unknown
LandscapeEditor.TimeStamp=2025.08.22-14.44.18
LandscapeEditor.LastCompileMethod=Unknown
FoliageEdit.TimeStamp=2025.08.22-14.44.13
FoliageEdit.LastCompileMethod=Unknown
VirtualTexturingEditor.TimeStamp=2025.08.22-14.44.41
VirtualTexturingEditor.LastCompileMethod=Unknown
AutomationWorker.TimeStamp=2025.08.22-14.43.55
AutomationWorker.LastCompileMethod=Unknown
SequenceRecorderSections.TimeStamp=2025.08.22-14.44.31
SequenceRecorderSections.LastCompileMethod=Unknown
StatsViewer.TimeStamp=2025.08.22-14.44.35
StatsViewer.LastCompileMethod=Unknown
DataLayerEditor.TimeStamp=2025.08.22-14.44.03
DataLayerEditor.LastCompileMethod=Unknown
AndroidDeviceProfileSelector.TimeStamp=2025.08.22-14.55.38
AndroidDeviceProfileSelector.LastCompileMethod=Unknown
GameProjectGeneration.TimeStamp=2025.08.22-14.44.13
GameProjectGeneration.LastCompileMethod=Unknown
UnsavedAssetsTracker.TimeStamp=2025.08.22-14.44.41
UnsavedAssetsTracker.LastCompileMethod=Unknown
StatusBar.TimeStamp=2025.08.22-14.44.35
StatusBar.LastCompileMethod=Unknown
SceneOutliner.TimeStamp=2025.08.22-14.44.30
SceneOutliner.LastCompileMethod=Unknown
SubobjectEditor.TimeStamp=2025.08.22-14.44.35
SubobjectEditor.LastCompileMethod=Unknown
AddContentDialog.TimeStamp=2025.08.22-14.43.52
AddContentDialog.LastCompileMethod=Unknown
WidgetCarousel.TimeStamp=2025.08.22-14.44.42
WidgetCarousel.LastCompileMethod=Unknown
HierarchicalLODOutliner.TimeStamp=2025.08.22-14.44.14
HierarchicalLODOutliner.LastCompileMethod=Unknown
UnrealMCP.TimeStamp=2025.08.26-02.43.02
UnrealMCP.LastCompileMethod=External
PCGCompute.TimeStamp=2025.08.22-14.55.28
PCGCompute.LastCompileMethod=Unknown
ChaosVehicles.TimeStamp=2025.08.22-14.54.05
ChaosVehicles.LastCompileMethod=Unknown
ChaosVehiclesEditor.TimeStamp=2025.08.22-14.54.05
ChaosVehiclesEditor.LastCompileMethod=Unknown
DataRegistry.TimeStamp=2025.08.22-14.55.42
DataRegistry.LastCompileMethod=Unknown
DataRegistryEditor.TimeStamp=2025.08.22-14.55.42
DataRegistryEditor.LastCompileMethod=Unknown
GameplayAbilities.TimeStamp=2025.08.22-14.55.43
GameplayAbilities.LastCompileMethod=Unknown
GameplayAbilitiesEditor.TimeStamp=2025.08.22-14.55.43
GameplayAbilitiesEditor.LastCompileMethod=Unknown
ReplicationGraph.TimeStamp=2025.08.22-14.56.15
ReplicationGraph.LastCompileMethod=Unknown
SmartObjectsModule.TimeStamp=2025.08.22-14.56.19
SmartObjectsModule.LastCompileMethod=Unknown
SmartObjectsTestSuite.TimeStamp=2025.08.22-14.56.19
SmartObjectsTestSuite.LastCompileMethod=Unknown
PCG.TimeStamp=2025.08.22-14.55.28
PCG.LastCompileMethod=Unknown
PCGEditor.TimeStamp=2025.08.22-14.55.28
PCGEditor.LastCompileMethod=Unknown
FieldSystemEditor.TimeStamp=2025.08.22-14.54.11
FieldSystemEditor.LastCompileMethod=Unknown
TargetingSystem.TimeStamp=2025.08.22-14.54.12
TargetingSystem.LastCompileMethod=Unknown
MassCommon.TimeStamp=2025.08.22-14.55.56
MassCommon.LastCompileMethod=Unknown
MassActors.TimeStamp=2025.08.22-14.55.56
MassActors.LastCompileMethod=Unknown
MassEQS.TimeStamp=2025.08.22-14.55.56
MassEQS.LastCompileMethod=Unknown
MassSignals.TimeStamp=2025.08.22-14.55.56
MassSignals.LastCompileMethod=Unknown
MassSpawner.TimeStamp=2025.08.22-14.55.56
MassSpawner.LastCompileMethod=Unknown
MassSmartObjects.TimeStamp=2025.08.22-14.55.56
MassSmartObjects.LastCompileMethod=Unknown
MassSimulation.TimeStamp=2025.08.22-14.55.56
MassSimulation.LastCompileMethod=Unknown
MassLOD.TimeStamp=2025.08.22-14.55.56
MassLOD.LastCompileMethod=Unknown
MassMovement.TimeStamp=2025.08.22-14.55.56
MassMovement.LastCompileMethod=Unknown
MassReplication.TimeStamp=2025.08.22-14.55.56
MassReplication.LastCompileMethod=Unknown
MassRepresentation.TimeStamp=2025.08.22-14.55.56
MassRepresentation.LastCompileMethod=Unknown
MassGameplayDebug.TimeStamp=2025.08.22-14.55.56
MassGameplayDebug.LastCompileMethod=Unknown
MassGameplayEditor.TimeStamp=2025.08.22-14.55.56
MassGameplayEditor.LastCompileMethod=Unknown
MassGameplayExternalTraits.TimeStamp=2025.08.22-14.55.56
MassGameplayExternalTraits.LastCompileMethod=Unknown
MassGameplayTestSuite.TimeStamp=2025.08.22-14.55.56
MassGameplayTestSuite.LastCompileMethod=Unknown
ZoneGraph.TimeStamp=2025.08.22-14.56.26
ZoneGraph.LastCompileMethod=Unknown
ZoneGraphTestSuite.TimeStamp=2025.08.22-14.56.26
ZoneGraphTestSuite.LastCompileMethod=Unknown
ZoneGraphDebug.TimeStamp=2025.08.22-14.56.26
ZoneGraphDebug.LastCompileMethod=Unknown
WorldConditions.TimeStamp=2025.08.22-14.56.26
WorldConditions.LastCompileMethod=Unknown
WorldConditionsTestSuite.TimeStamp=2025.08.22-14.56.26
WorldConditionsTestSuite.LastCompileMethod=Unknown
ZoneGraphAnnotations.TimeStamp=2025.08.22-14.56.26
ZoneGraphAnnotations.LastCompileMethod=Unknown
MassMovementEditor.TimeStamp=2025.08.22-14.55.56
MassMovementEditor.LastCompileMethod=Unknown
SmartObjectsEditorModule.TimeStamp=2025.08.22-14.56.19
SmartObjectsEditorModule.LastCompileMethod=Unknown
ZoneGraphEditor.TimeStamp=2025.08.22-14.56.26
ZoneGraphEditor.LastCompileMethod=Unknown
WorldConditionsEditor.TimeStamp=2025.08.22-14.56.26
WorldConditionsEditor.LastCompileMethod=Unknown
NewLevelDialog.TimeStamp=2025.08.22-14.44.23
NewLevelDialog.LastCompileMethod=Unknown
HierarchicalLODUtilities.TimeStamp=2025.08.22-14.44.14
HierarchicalLODUtilities.LastCompileMethod=Unknown

[Python]
LastDirectory=
RecentsFiles=C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py

[PluginBrowser]
InstalledPlugins=GeneSplicer

[ContentBrowser]
ContentBrowserDrawer.SelectedPaths=/Game
ContentBrowserDrawer.PluginFilters=
ContentBrowserDrawer.SourcesExpanded=True
ContentBrowserDrawer.IsLocked=False
ContentBrowserDrawer.FavoritesAreaExpanded=False
ContentBrowserDrawer.FavoritesSearchAreaExpanded=False
ContentBrowserDrawer.PathAreaExpanded=True
ContentBrowserDrawer.PathSearchAreaExpanded=False
ContentBrowserDrawer.VerticalSplitter.FixedSlotSize0=230
ContentBrowserDrawer.VerticalSplitter.SlotSize1=1
ContentBrowserDrawer.VerticalSplitter.SlotSize2=1
ContentBrowserDrawer.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserDrawer.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserDrawer.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserDrawer.Favorites.SelectedPaths=
FavoritePaths=
AssetDialog.ThumbnailSizeGrid=3
AssetDialog.ThumbnailSizeList=3
AssetDialog.ThumbnailSizeCustom=3
AssetDialog.ThumbnailSizeColumn=0
AssetDialog.CurrentViewType=1
AssetDialog.ZoomScale=0
AssetDialog.ListViewColumnsManuallyChangedOnce=False
AssetDialog.ColumnViewColumnsManuallyChangedOnce=False
AssetDialog.ListHiddenColumns=Class
ContentBrowserDrawer.SelectedCollections=
ContentBrowserDrawer.ExpandedCollections=
ContentBrowserDrawer.CollectionAreaExpanded=False
ContentBrowserDrawer.CollectionSearchAreaExpanded=False
ContentBrowserDrawer.ThumbnailSizeGrid=2
ContentBrowserDrawer.ThumbnailSizeList=2
ContentBrowserDrawer.ThumbnailSizeCustom=2
ContentBrowserDrawer.ThumbnailSizeColumn=0
ContentBrowserDrawer.CurrentViewType=1
ContentBrowserDrawer.ZoomScale=0
ContentBrowserDrawer.ListViewColumnsManuallyChangedOnce=False
ContentBrowserDrawer.ColumnViewColumnsManuallyChangedOnce=False
ContentBrowserDrawer.JumpMRU=/All/Game
ContentBrowserDrawer.JumpMRU=/Game
ContentBrowserDrawer.JumpMRU=/All/Game/Blueprints
ContentBrowserDrawer.JumpMRU=/All/Plugins
ContentBrowserDrawer.JumpMRU=/All/Game/Materials

